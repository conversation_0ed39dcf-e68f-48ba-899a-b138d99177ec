#!/usr/bin/env python3
"""
基于ninjax的任务感知分割训练脚本
专门处理收集的多样化数据

使用方法:
python scripts/run_ninjax_training.py
python scripts/run_ninjax_training.py --epochs 30 --batch-size 8
"""

import os
import sys
import argparse
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tools.ninjax_diverse_trainer import NinjaxDiverseTrainer, create_ninjax_config


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Ninjax任务感知分割训练')
    
    parser.add_argument('--data-dir', type=str, default='data/episodes_diverse',
                       help='数据目录')
    parser.add_argument('--output-dir', type=str, default='training_output_ninjax',
                       help='输出目录')
    parser.add_argument('--epochs', type=int, default=20,
                       help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=4,
                       help='批次大小')
    parser.add_argument('--learning-rate', type=float, default=1e-4,
                       help='学习率')
    parser.add_argument('--feature-dim', type=int, default=256,
                       help='特征维度')
    parser.add_argument('--debug', action='store_true',
                       help='调试模式')
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    print("🚀 Ninjax任务感知分割训练开始")
    print(f"   数据目录: {args.data_dir}")
    print(f"   输出目录: {args.output_dir}")
    print(f"   训练轮数: {args.epochs}")
    print(f"   批次大小: {args.batch_size}")
    print(f"   学习率: {args.learning_rate}")
    print(f"   调试模式: {args.debug}")
    
    # 检查数据目录
    data_dir = Path(args.data_dir)
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    # 统计数据
    episode_dirs = list(data_dir.glob('*/ep_*'))
    print(f"   找到 {len(episode_dirs)} 个episode目录")
    
    if len(episode_dirs) == 0:
        print("❌ 没有找到episode数据")
        return
    
    # 创建配置
    config = create_ninjax_config()
    config.update({
        'data_dir': args.data_dir,
        'output_dir': args.output_dir,
        'num_epochs': args.epochs,
        'batch_size': args.batch_size,
        'learning_rate': args.learning_rate,
        'feature_dim': args.feature_dim,
    })
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 初始化训练器
    try:
        trainer = NinjaxDiverseTrainer(config)
    except Exception as e:
        print(f"❌ 训练器初始化失败: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return
    
    # 开始训练
    print("\n🎯 开始训练...")
    start_time = time.time()
    
    try:
        training_history = trainer.train()
        
        training_time = time.time() - start_time
        print(f"\n🎉 训练完成!")
        print(f"   总时间: {training_time:.2f}秒")
        print(f"   输出目录: {output_dir}")
        
        # 生成训练报告
        report = {
            'training_time': training_time,
            'config': config,
            'data_stats': trainer.data_stats,
            'final_loss': training_history['losses'][-1] if training_history and training_history.get('losses') else 0,
        }
        
        import pickle
        with open(output_dir / 'training_report.pkl', 'wb') as f:
            pickle.dump(report, f)
        
        print(f"   训练报告: {output_dir / 'training_report.pkl'}")
        
        # 显示数据类型性能
        print("\n📊 各数据类型性能:")
        if training_history and 'data_type_performance' in training_history:
            for data_type, scores in training_history['data_type_performance'].items():
                if scores:
                    avg_score = sum(scores) / len(scores)
                    print(f"   {data_type:>8}: 平均评分 {avg_score:.3f}")
                else:
                    print(f"   {data_type:>8}: 无数据")
        else:
            print("   无性能数据可显示")
        
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
        
    except Exception as e:
        print(f"\n❌ 训练过程中发生错误: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()


if __name__ == '__main__':
    main()
