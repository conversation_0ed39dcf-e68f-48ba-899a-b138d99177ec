#!/bin/bash

# 多样化数据训练启动脚本
# 专门针对你收集的四阶段数据进行训练

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DATA_DIR="$PROJECT_DIR/data/episodes_diverse"
OUTPUT_DIR="$PROJECT_DIR/training_output_diverse"
VALIDATE_ONLY=false
EPOCHS=100
BATCH_SIZE=16
LEARNING_RATE=1e-4

# 帮助信息
show_help() {
    cat << EOF
多样化数据训练启动脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -d, --data-dir DIR      数据目录 (默认: data/episodes_diverse)
    -o, --output-dir DIR    输出目录 (默认: training_output_diverse)
    -e, --epochs NUM        训练轮数 (默认: 100)
    -b, --batch-size NUM    批次大小 (默认: 16)
    -l, --learning-rate NUM 学习率 (默认: 1e-4)
    --validate-only         只验证数据，不训练
    --resume                从检查点恢复训练

示例:
    $0                                    # 使用默认参数训练
    $0 --validate-only                   # 只验证数据质量
    $0 --epochs 200 --batch-size 32      # 自定义训练参数
    $0 --resume                          # 从检查点恢复
EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--data-dir)
            DATA_DIR="$2"
            shift 2
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -e|--epochs)
            EPOCHS="$2"
            shift 2
            ;;
        -b|--batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        -l|--learning-rate)
            LEARNING_RATE="$2"
            shift 2
            ;;
        --validate-only)
            VALIDATE_ONLY=true
            shift
            ;;
        --resume)
            RESUME=true
            shift
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 打印配置信息
print_info "=== 多样化数据训练配置 ==="
print_info "项目目录: $PROJECT_DIR"
print_info "数据目录: $DATA_DIR"
print_info "输出目录: $OUTPUT_DIR"
print_info "训练轮数: $EPOCHS"
print_info "批次大小: $BATCH_SIZE"
print_info "学习率: $LEARNING_RATE"
print_info "只验证: $VALIDATE_ONLY"
echo

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查Python
    if ! command -v python &> /dev/null; then
        print_error "Python未安装"
        exit 1
    fi
    
    # 检查数据目录
    if [[ ! -d "$DATA_DIR" ]]; then
        print_error "数据目录不存在: $DATA_DIR"
        print_info "请确保已完成数据收集阶段"
        exit 1
    fi
    
    # 检查必要的Python包
    python -c "import jax, numpy, PIL" 2>/dev/null || {
        print_error "缺少必要的Python包 (jax, numpy, PIL)"
        print_info "请安装: pip install jax numpy pillow matplotlib"
        exit 1
    }
    
    print_success "依赖检查完成"
}

# 创建输出目录
create_output_dir() {
    if [[ ! -d "$OUTPUT_DIR" ]]; then
        print_info "创建输出目录: $OUTPUT_DIR"
        mkdir -p "$OUTPUT_DIR"
    fi
}

# 验证数据
validate_data() {
    print_info "验证数据质量..."
    
    cd "$PROJECT_DIR"
    python tools/validate_diverse_data.py
    
    if [[ $? -eq 0 ]]; then
        print_success "数据验证完成"
        
        # 移动验证结果到输出目录
        if [[ -f "data_validation_report.txt" ]]; then
            mv data_validation_report.txt "$OUTPUT_DIR/"
        fi
        if [[ -f "data_summary.png" ]]; then
            mv data_summary.png "$OUTPUT_DIR/"
        fi
        if [[ -f "reward_distributions.png" ]]; then
            mv reward_distributions.png "$OUTPUT_DIR/"
        fi
    else
        print_error "数据验证失败"
        exit 1
    fi
}

# 训练模型
train_model() {
    print_info "开始训练模型..."
    
    cd "$PROJECT_DIR"
    
    # 构建Python命令
    PYTHON_CMD="python tools/diverse_data_trainer.py"
    
    # 设置环境变量传递参数
    export DIVERSE_DATA_DIR="$DATA_DIR"
    export DIVERSE_OUTPUT_DIR="$OUTPUT_DIR"
    export DIVERSE_EPOCHS="$EPOCHS"
    export DIVERSE_BATCH_SIZE="$BATCH_SIZE"
    export DIVERSE_LEARNING_RATE="$LEARNING_RATE"
    
    if [[ "$RESUME" == true ]]; then
        export DIVERSE_RESUME="true"
    fi
    
    print_info "执行命令: $PYTHON_CMD"
    print_info "环境变量:"
    print_info "  DIVERSE_DATA_DIR=$DIVERSE_DATA_DIR"
    print_info "  DIVERSE_OUTPUT_DIR=$DIVERSE_OUTPUT_DIR"
    print_info "  DIVERSE_EPOCHS=$DIVERSE_EPOCHS"
    print_info "  DIVERSE_BATCH_SIZE=$DIVERSE_BATCH_SIZE"
    print_info "  DIVERSE_LEARNING_RATE=$DIVERSE_LEARNING_RATE"
    
    # 运行训练
    if eval "$PYTHON_CMD"; then
        print_success "模型训练完成"
    else
        print_error "模型训练失败"
        exit 1
    fi
}

# 生成训练报告
generate_report() {
    print_info "生成训练报告..."
    
    REPORT_FILE="$OUTPUT_DIR/training_report.md"
    
    cat > "$REPORT_FILE" << EOF
# 多样化数据训练报告

## 训练配置
- 数据目录: $DATA_DIR
- 输出目录: $OUTPUT_DIR
- 训练轮数: $EPOCHS
- 批次大小: $BATCH_SIZE
- 学习率: $LEARNING_RATE
- 训练时间: $(date)

## 数据统计
请查看以下文件获取详细信息:
- \`data_validation_report.txt\` - 数据验证报告
- \`data_summary.png\` - 数据分布可视化
- \`reward_distributions.png\` - 奖励分布图

## 训练结果
请查看以下文件获取训练结果:
- \`final_diverse_model.pkl\` - 最终训练模型
- \`training_history.pkl\` - 训练历史数据
- \`training_curves.png\` - 训练曲线图

## 模型文件
- 最终模型: \`final_diverse_model.pkl\`
- 检查点: \`checkpoint_epoch_*.pkl\`

## 使用方法
\`\`\`python
from tools.task_aware_passability_trainer import TaskAwarePassabilityTrainer

# 加载训练好的模型
trainer = TaskAwarePassabilityTrainer()
trainer.load_model('$OUTPUT_DIR/final_diverse_model.pkl')

# 进行推理
# ... 你的推理代码
\`\`\`

---
生成时间: $(date)
EOF

    print_success "训练报告生成: $REPORT_FILE"
}

# 主函数
main() {
    print_info "=== 开始多样化数据训练 ==="
    
    # 检查依赖
    check_dependencies
    
    # 创建输出目录
    create_output_dir
    
    # 验证数据
    validate_data
    
    # 如果只验证数据，则退出
    if [[ "$VALIDATE_ONLY" == true ]]; then
        print_success "=== 数据验证完成 ==="
        exit 0
    fi
    
    # 训练模型
    train_model
    
    # 生成报告
    generate_report
    
    print_success "=== 多样化数据训练完成 ==="
    print_info "输出文件位于: $OUTPUT_DIR"
    print_info "主要文件:"
    print_info "  - final_diverse_model.pkl (最终模型)"
    print_info "  - training_curves.png (训练曲线)"
    print_info "  - training_report.md (训练报告)"
}

# 运行主函数
main "$@"
