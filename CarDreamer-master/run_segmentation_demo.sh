#!/bin/bash

# 分割网络演示启动脚本
# 展示任务感知分割网络在DreamerV3中的应用

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🎯 Task-aware Segmentation Demo${NC}"
echo "=================================="

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查Python环境
echo -e "${BLUE}🔍 Checking Python environment...${NC}"
if ! command -v python &> /dev/null; then
    echo -e "${RED}❌ Python not found${NC}"
    exit 1
fi

# 检查必要的包
echo -e "${BLUE}📦 Checking required packages...${NC}"
python -c "import jax, matplotlib, numpy, cv2" 2>/dev/null || {
    echo -e "${YELLOW}⚠️  Some packages may be missing. Installing...${NC}"
    pip install jax jaxlib matplotlib numpy opencv-python
}

# 检查CARLA（可选）
echo -e "${BLUE}🚗 Checking CARLA availability...${NC}"
if python -c "import carla" 2>/dev/null; then
    echo -e "${GREEN}✅ CARLA available${NC}"
    CARLA_AVAILABLE=true
else
    echo -e "${YELLOW}⚠️  CARLA not available (demo will use test images only)${NC}"
    CARLA_AVAILABLE=false
fi

# 检查模型文件
echo -e "${BLUE}🧠 Checking model files...${NC}"
MODEL_PATHS=(
    "checkpoints/h_stage/final_h_stage_model.pkl"
    "checkpoints/final_h_stage_model.pkl"
    "training_output/segmentation_model.pkl"
    "training_output_diverse/final_diverse_model.pkl"
)

MODEL_FOUND=false
for path in "${MODEL_PATHS[@]}"; do
    if [[ -f "$path" ]]; then
        echo -e "${GREEN}✅ Found model: $path${NC}"
        MODEL_FOUND=true
        break
    fi
done

if [[ "$MODEL_FOUND" == false ]]; then
    echo -e "${YELLOW}⚠️  No pretrained model found. Demo will use random initialization.${NC}"
fi

# 创建输出目录
mkdir -p segmentation_demo_output

echo ""
echo -e "${BLUE}🚀 Starting segmentation demo...${NC}"
echo ""

# 运行演示
python tools/demo_segmentation_in_dreamerv3.py

echo ""
echo -e "${GREEN}✅ Demo completed!${NC}"
echo -e "${BLUE}📁 Check segmentation_demo_output/ for saved results${NC}"
