.pytest_cache
dist
__pycache__/
*.py[cod]
*.egg-info
MUJOCO_LOG.TXT
;
wandb/
*.ipynb
*h5
traffic_scenarios/
*.log
.nfs*
*logdir/
.vscode/
.idea/

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# ===== 项目特定数据文件和模型文件 =====
# Checkpoint文件
*.ckpt
*.checkpoint
checkpoints/
checkpoint_*/

# 模型权重文件
*.pkl
*.pth
*.pt
*.h5
*.hdf5
*.weights
*.model

# 训练输出目录
training_output*/
evaluation_output*/
logs/
runs/
tensorboard_logs/

# 数据目录
data/
datasets/
episodes/
episodes_*/
collected_data/
replay_buffer/

# ===== 图像和可视化文件 =====
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff
*.svg
*.pdf
*.eps

# 视频文件
*.mp4
*.avi
*.mov
*.mkv
*.webm

# ===== 压缩文件和归档 =====
*.zip
*.tar
*.tar.gz
*.tar.bz2
*.rar
*.7z
*.gz
*.bz2

# ===== 机器学习和深度学习 =====
# TensorFlow
*.tfevents.*
*.pb
saved_model/
tf_logs/

# PyTorch
*.pth.tar

# JAX
*.jax

# MLflow
mlruns/

# ===== 系统文件 =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== 临时文件和缓存 =====
*.tmp
*.temp
*.out
*.err
.cache/
cache/
tmp/
temp/

# ===== CARLA相关 =====
CarlaUE4.log
CarlaUE4-*.log

# ===== 项目特定 =====
# 实验结果
results/
experiments/
outputs/

# 备份文件
*.bak
*.backup
*_backup
*_old

# 大文件标记
*.large
*.big

# 测试输出
test_output/
test_results/
