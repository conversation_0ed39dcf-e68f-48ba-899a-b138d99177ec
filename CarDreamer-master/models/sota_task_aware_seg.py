"""
SOTA Task-aware Segmentation Network - Ninjax版本
基于ninjax框架的任务感知分割网络

技术特点：
- 基于ninjax框架，与CarDreamer完全兼容
- 二分类可通行性分割
- 任务感知特征学习
- 端到端集成DreamerV3
"""

import jax
import jax.numpy as jnp
from jax import random
from typing import Dict, List, Tuple, Optional, Any
import numpy as np

# 添加项目路径
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from dreamerv3 import ninjax as nj

f32 = jnp.float32


class NinjaxTaskAwareSegNet(nj.Module):
    """
    基于ninjax的任务感知分割网络

    特点：
    1. 严格遵循ninjax框架规范
    2. 二分类可通行性分割
    3. 任务感知特征学习
    4. 与DreamerV3无缝集成
    """

    def __init__(self, feature_dim=256, task_context_dim=64, name='ninjax_task_seg'):
        # ninjax Module必须提供name
        super().__init__(name=name)
        self.feature_dim = feature_dim
        self.task_context_dim = task_context_dim

    def __call__(self, rgb_input, task_context=None):
        """
        前向传播 - 使用真正的卷积操作
        Args:
            rgb_input: (B, H, W, 3) RGB图像
            task_context: (B, task_context_dim) 任务上下文
        Returns:
            Dict包含分割结果和特征
        """
        B, H, W, C = rgb_input.shape

        # 真正的卷积编码器
        # Stage 1: 初始卷积 (224x224 -> 112x112)
        x = nj.Conv2D(32, 3, strides=2, padding='SAME', name='conv1')(rgb_input)
        x = jax.nn.relu(x)

        # Stage 2: 特征提取 (112x112 -> 56x56)
        x = nj.Conv2D(64, 3, strides=2, padding='SAME', name='conv2')(x)
        x = jax.nn.relu(x)

        # Stage 3: 深层特征 (56x56 -> 28x28)
        x = nj.Conv2D(128, 3, strides=2, padding='SAME', name='conv3')(x)
        x = jax.nn.relu(x)

        # Stage 4: 高级特征 (28x28 -> 14x14)
        x = nj.Conv2D(256, 3, strides=2, padding='SAME', name='conv4')(x)
        x = jax.nn.relu(x)

        # 全局平均池化获取全局特征
        global_feat = jnp.mean(x, axis=(1, 2))  # (B, 256)

        # 任务上下文融合
        if task_context is not None:
            # 任务上下文投影
            context_proj = nj.Linear(256, name='context_proj')(task_context)
            # 注意力机制
            attention_weights = nj.Linear(256, name='attention_weights')(context_proj)
            attention_weights = jax.nn.sigmoid(attention_weights)
            # 特征融合
            global_feat = global_feat * (1 + 0.5 * attention_weights) + 0.3 * context_proj

        # 卷积解码器：生成可通行性概率图
        # 从全局特征重建空间特征
        spatial_feat = nj.Linear(14*14*128, name='spatial_proj')(global_feat)
        spatial_feat = spatial_feat.reshape(B, 14, 14, 128)

        # 上采样解码器 (14x14 -> 28x28)
        x = nj.Conv2D(128, 3, padding='SAME', name='decode1')(spatial_feat)
        x = jax.nn.relu(x)
        x = jax.image.resize(x, (B, 28, 28, 128), method='bilinear')

        # 上采样 (28x28 -> 56x56)
        x = nj.Conv2D(64, 3, padding='SAME', name='decode2')(x)
        x = jax.nn.relu(x)
        x = jax.image.resize(x, (B, 56, 56, 64), method='bilinear')

        # 最终分割输出 (56x56 -> 56x56)
        passability_logits = nj.Conv2D(1, 3, padding='SAME', name='passability_head')(x)
        passability_prob = jax.nn.sigmoid(passability_logits)

        # 任务感知评分
        task_score = nj.Linear(1, name='task_score_head')(global_feat)
        task_score = task_score.squeeze(-1)  # (B,)

        # 特征嵌入用于DreamerV3
        embed_feat = nj.Linear(128, name='embed_head')(global_feat)

        return {
            'passability_prob': passability_prob,      # (B, H/4, W/4, 1)
            'passability_logits': passability_logits,  # (B, H/4, W/4, 1)
            'task_score': task_score,                  # (B,)
            'embed_feat': embed_feat,                  # (B, 128)
            'global_feat': global_feat,                # (B, 256)
        }


# 简化的伪标签生成器
class PseudoLabelGenerator(nj.Module):
    """基于ninjax的伪标签生成器"""

    def __init__(self, name='pseudo_label_gen'):
        super().__init__(name=name)

    def __call__(self, data_type, reward, image_shape):
        """
        基于数据类型和奖励生成伪标签
        Args:
            data_type: 数据类型 ('expert', 'cross', 'noisy', 'hard')
            reward: 奖励值
            image_shape: 图像尺寸
        Returns:
            伪标签
        """
        h, w = image_shape[:2]

        # 根据数据类型设置基础概率
        if data_type == 'expert':
            base_prob = 0.7 + 0.2 * jnp.clip(reward, 0, 1)
            noise_level = 0.1
        elif data_type == 'cross':
            base_prob = 0.2 + 0.1 * jnp.clip(reward, -1, 0)
            noise_level = 0.2
        elif data_type == 'noisy':
            base_prob = 0.4 + 0.2 * reward
            noise_level = 0.3
        elif data_type == 'hard':
            base_prob = 0.3 + 0.3 * jnp.clip(reward, -1, 1)
            noise_level = 0.2
        else:
            base_prob = 0.5
            noise_level = 0.2

        # 生成伪标签
        rng = random.PRNGKey(42)
        noise = random.normal(rng, (h//4, w//4, 1)) * noise_level
        label = jnp.full((h//4, w//4, 1), base_prob) + noise
        label = jnp.clip(label, 0.0, 1.0)

        return label


# 损失函数
class TaskAwareLoss(nj.Module):
    """基于ninjax的任务感知损失函数"""

    def __init__(self, name='task_aware_loss'):
        super().__init__(name=name)

    def __call__(self, pred_outputs, pseudo_labels, rewards, data_types):
        """
        计算任务感知损失
        """
        pred_prob = pred_outputs['passability_prob']
        task_scores = pred_outputs['task_score']

        # 1. 分割损失 (BCE + Dice)
        bce_loss = -jnp.mean(
            pseudo_labels * jnp.log(pred_prob + 1e-8) +
            (1 - pseudo_labels) * jnp.log(1 - pred_prob + 1e-8)
        )

        # Dice loss
        intersection = jnp.sum(pred_prob * pseudo_labels, axis=(1,2,3))
        union = jnp.sum(pred_prob, axis=(1,2,3)) + jnp.sum(pseudo_labels, axis=(1,2,3))
        dice_loss = 1 - jnp.mean((2 * intersection + 1e-8) / (union + 1e-8))

        seg_loss = bce_loss + dice_loss

        # 2. 任务感知损失
        task_loss = jnp.mean((task_scores - rewards) ** 2)

        # 3. 数据类型特定损失
        type_losses = []
        for i, data_type in enumerate(data_types):
            if data_type == 'expert':
                type_loss = jnp.maximum(0, 0.3 - task_scores[i])
            elif data_type == 'cross':
                type_loss = jnp.maximum(0, task_scores[i] - (-0.3))
            else:
                type_loss = 0.0
            type_losses.append(type_loss)

        type_loss = jnp.mean(jnp.array(type_losses))

        # 总损失
        total_loss = seg_loss + 0.5 * task_loss + 0.3 * type_loss

        return total_loss, {
            'seg_loss': seg_loss,
            'bce_loss': bce_loss,
            'dice_loss': dice_loss,
            'task_loss': task_loss,
            'type_loss': type_loss,
            'total_loss': total_loss
        }
