"""
快速分割网络测试
简单测试分割网络的基本功能
"""
import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import jax
import jax.numpy as jnp

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 导入模型
try:
    from models.passability_seg_sota import TaskAwarePassabilitySegNet
    from dreamerv3 import ninjax as nj
    print("✅ Successfully imported segmentation model")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


def create_simple_test_image():
    """创建简单的测试图像"""
    # 创建一个简单的道路场景
    img = np.zeros((128, 128, 3), dtype=np.float32)
    
    # 添加道路（灰色）
    img[50:78, :, :] = [0.5, 0.5, 0.5]
    
    # 添加车道线（白色）
    img[49:50, :, :] = [1.0, 1.0, 1.0]
    img[78:79, :, :] = [1.0, 1.0, 1.0]
    img[63:65, :, :] = [1.0, 1.0, 1.0]  # 中央分隔线
    
    # 添加一些障碍物（红色）
    img[55:65, 30:40, :] = [1.0, 0.0, 0.0]
    
    return img


def test_segmentation_model():
    """测试分割模型"""
    print("🧠 Testing segmentation model...")
    
    # 创建模型
    model = TaskAwarePassabilitySegNet(
        feature_channels=64,
        downsample_ratio=8,
        use_attention=True,
        use_contrastive=True,
        use_temporal=True,
        name='test_seg'
    )
    
    # 创建测试输入
    test_image = create_simple_test_image()
    batch_image = jnp.expand_dims(jnp.array(test_image), axis=0)  # (1, 128, 128, 3)
    
    # 创建任务上下文
    reward = 1.0
    task_context = jnp.array([[reward, reward**2, np.abs(reward)]])  # (1, 3)
    
    print(f"Input image shape: {batch_image.shape}")
    print(f"Task context shape: {task_context.shape}")
    
    try:
        # 运行前向传播
        def forward_fn(x, task_ctx):
            return model(x, task_context=task_ctx, prev_features=None)
        
        # 使用ninjax的pure函数
        pure_forward = nj.pure(forward_fn)
        key = jax.random.PRNGKey(42)
        
        print("🔄 Running forward pass...")
        outputs, params = pure_forward({}, key, batch_image, task_context)
        
        print("✅ Forward pass successful!")
        print(f"Output keys: {list(outputs.keys())}")
        
        # 打印输出形状
        for key, value in outputs.items():
            if isinstance(value, jnp.ndarray):
                print(f"  {key}: {value.shape}")
            else:
                print(f"  {key}: {type(value)}")
        
        return test_image, outputs, params
        
    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None


def visualize_results(image, outputs):
    """可视化结果"""
    if outputs is None:
        print("❌ No outputs to visualize")
        return
    
    print("🎨 Creating visualization...")
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('Task-aware Segmentation Test Results', fontsize=16)
    
    # 原始图像
    axes[0, 0].imshow(image)
    axes[0, 0].set_title('Original Image')
    axes[0, 0].axis('off')
    
    # 可通行性概率图
    if 'prob' in outputs:
        prob_map = outputs['prob'][0, :, :, 0]  # (H, W)
        im1 = axes[0, 1].imshow(prob_map, cmap='RdYlGn', vmin=0, vmax=1)
        axes[0, 1].set_title('Passability Probability')
        axes[0, 1].axis('off')
        plt.colorbar(im1, ax=axes[0, 1], fraction=0.046, pad=0.04)
    else:
        axes[0, 1].text(0.5, 0.5, 'No probability map', ha='center', va='center')
        axes[0, 1].axis('off')
    
    # 特征图（前3个通道）
    if 'feat' in outputs:
        feat_map = outputs['feat'][0, :, :, :3]  # (H, W, 3)
        # 归一化到[0,1]
        feat_map = (feat_map - feat_map.min()) / (feat_map.max() - feat_map.min() + 1e-8)
        axes[1, 0].imshow(feat_map)
        axes[1, 0].set_title('Feature Map (RGB)')
        axes[1, 0].axis('off')
    else:
        axes[1, 0].text(0.5, 0.5, 'No feature map', ha='center', va='center')
        axes[1, 0].axis('off')
    
    # 评分信息
    scores = []
    labels = []
    
    if 'score' in outputs:
        scores.append(float(outputs['score'][0]))
        labels.append('Basic Score')
    
    if 'task_score' in outputs:
        scores.append(float(outputs['task_score'][0]))
        labels.append('Task Score')
    
    if scores:
        axes[1, 1].bar(labels, scores, color=['blue', 'orange'][:len(scores)])
        axes[1, 1].set_title('Segmentation Scores')
        axes[1, 1].set_ylabel('Score')
    else:
        axes[1, 1].text(0.5, 0.5, 'No scores available', ha='center', va='center')
        axes[1, 1].axis('off')
    
    plt.tight_layout()
    
    # 保存结果
    output_path = 'quick_seg_test_result.png'
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    print(f"✅ Results saved to: {output_path}")
    
    # 显示结果
    plt.show()


def main():
    """主函数"""
    print("🎯 Quick Segmentation Test")
    print("=" * 30)
    
    # 测试模型
    image, outputs, params = test_segmentation_model()
    
    if outputs is not None:
        # 可视化结果
        visualize_results(image, outputs)
        
        # 打印一些统计信息
        print("\n📊 Results Summary:")
        if 'score' in outputs:
            print(f"  Basic Score: {float(outputs['score'][0]):.4f}")
        if 'task_score' in outputs:
            print(f"  Task Score: {float(outputs['task_score'][0]):.4f}")
        if 'prob' in outputs:
            prob_map = outputs['prob'][0, :, :, 0]
            print(f"  Probability Map - Min: {float(prob_map.min()):.4f}, Max: {float(prob_map.max()):.4f}, Mean: {float(prob_map.mean()):.4f}")
        
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")


if __name__ == "__main__":
    main()
