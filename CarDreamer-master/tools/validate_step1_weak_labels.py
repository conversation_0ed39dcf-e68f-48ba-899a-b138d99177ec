"""
验证第一步：小人/车辆漫游 → 生成弱标签 → 训练二分类分割网络

这个脚本验证：
1. 数据收集是否产生了有效的弱标签
2. 弱标签的质量如何（奖励信号与可通行性的关系）
3. 二分类分割网络是否能从弱标签中学习
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import jax
import jax.numpy as jnp
from pathlib import Path
import pickle
from typing import Dict, List, Tuple
import cv2

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from models.passability_seg_sota import TaskAwarePassabilitySegNet
from tools.task_aware_passability_trainer import TaskAwarePassabilityTrainer
from dreamerv3 import ninjax as nj


class Step1Validator:
    """第一步验证器"""
    
    def __init__(self, data_dir="data/episodes_diverse"):
        self.data_dir = Path(data_dir)
        self.results = {}
        
    def load_collected_data(self):
        """加载收集的数据"""
        print("🔍 加载收集的数据...")
        
        all_data = []
        data_stats = {
            'total_episodes': 0,
            'total_frames': 0,
            'reward_distribution': [],
            'data_types': {}
        }
        
        # 遍历所有数据目录
        for data_type_dir in self.data_dir.iterdir():
            if not data_type_dir.is_dir():
                continue
                
            data_type = data_type_dir.name
            episodes = []
            
            print(f"  📁 处理数据类型: {data_type}")
            
            for ep_dir in data_type_dir.iterdir():
                if not ep_dir.is_dir() or not ep_dir.name.startswith('ep_'):
                    continue
                    
                meta_file = ep_dir / 'meta.npz'
                rgb_dir = ep_dir / 'rgb'
                
                if meta_file.exists() and rgb_dir.exists():
                    try:
                        # 加载元数据
                        meta_data = np.load(meta_file)
                        rewards = meta_data['rewards']
                        actions = meta_data['actions']
                        
                        # 加载部分图像（避免内存溢出）
                        # 尝试不同的图像格式
                        rgb_files = []
                        for ext in ['*.png', '*.jpg', '*.jpeg']:
                            rgb_files.extend(list(rgb_dir.glob(ext)))
                        rgb_files = sorted(rgb_files)[:50]  # 只取前50帧
                        images = []

                        for rgb_file in rgb_files:
                            img = cv2.imread(str(rgb_file))
                            if img is not None:
                                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                                img = cv2.resize(img, (128, 128))  # 统一尺寸
                                images.append(img)
                        
                        if len(images) > 0:
                            episode_data = {
                                'data_type': data_type,
                                'episode': ep_dir.name,
                                'images': np.array(images),
                                'rewards': rewards[:len(images)],
                                'actions': actions[:len(images)],
                                'avg_reward': np.mean(rewards),
                                'reward_std': np.std(rewards)
                            }
                            
                            episodes.append(episode_data)
                            data_stats['total_frames'] += len(images)
                            data_stats['reward_distribution'].extend(rewards[:len(images)])
                            
                    except Exception as e:
                        print(f"    ⚠️  加载失败 {ep_dir.name}: {e}")
                        continue
            
            data_stats['data_types'][data_type] = len(episodes)
            data_stats['total_episodes'] += len(episodes)
            all_data.extend(episodes)
        
        print(f"✅ 数据加载完成:")
        print(f"   总episodes: {data_stats['total_episodes']}")
        print(f"   总帧数: {data_stats['total_frames']}")

        if len(data_stats['reward_distribution']) > 0:
            print(f"   奖励范围: [{np.min(data_stats['reward_distribution']):.3f}, {np.max(data_stats['reward_distribution']):.3f}]")
        else:
            print("   奖励范围: 无数据")
        
        return all_data, data_stats
    
    def analyze_weak_labels(self, all_data, data_stats):
        """分析弱标签质量"""
        print("\n🔬 分析弱标签质量...")
        
        # 1. 奖励分布分析
        rewards = data_stats['reward_distribution']
        
        plt.figure(figsize=(15, 10))
        
        # 奖励分布直方图
        plt.subplot(2, 3, 1)
        plt.hist(rewards, bins=50, alpha=0.7, color='blue')
        plt.title('奖励分布')
        plt.xlabel('奖励值')
        plt.ylabel('频次')
        
        # 不同数据类型的奖励分布
        plt.subplot(2, 3, 2)
        for data_type, episodes in data_stats['data_types'].items():
            type_rewards = []
            for episode in all_data:
                if episode['data_type'] == data_type:
                    type_rewards.extend(episode['rewards'])
            
            if len(type_rewards) > 0:
                plt.hist(type_rewards, bins=30, alpha=0.6, label=data_type)
        
        plt.title('不同数据类型的奖励分布')
        plt.xlabel('奖励值')
        plt.ylabel('频次')
        plt.legend()
        
        # 2. 弱标签生成规则验证
        plt.subplot(2, 3, 3)
        
        # 根据奖励生成弱标签
        weak_labels = []
        for reward in rewards:
            # 使用与训练器相同的规则
            reward_clipped = np.clip(reward, -5.0, 5.0)
            label = (reward_clipped + 5.0) / 10.0  # 映射到[0,1]
            weak_labels.append(label)
        
        plt.scatter(rewards, weak_labels, alpha=0.5, s=1)
        plt.title('奖励 → 弱标签映射')
        plt.xlabel('原始奖励')
        plt.ylabel('弱标签 (可通行性)')
        
        # 3. 数据类型分析
        plt.subplot(2, 3, 4)
        type_avg_rewards = {}
        type_avg_labels = {}
        
        for data_type in data_stats['data_types'].keys():
            type_rewards = []
            for episode in all_data:
                if episode['data_type'] == data_type:
                    type_rewards.extend(episode['rewards'])
            
            if len(type_rewards) > 0:
                avg_reward = np.mean(type_rewards)
                avg_label = (np.clip(avg_reward, -5.0, 5.0) + 5.0) / 10.0
                
                type_avg_rewards[data_type] = avg_reward
                type_avg_labels[data_type] = avg_label
        
        types = list(type_avg_rewards.keys())
        avg_rewards_list = [type_avg_rewards[t] for t in types]
        avg_labels_list = [type_avg_labels[t] for t in types]
        
        x_pos = np.arange(len(types))
        plt.bar(x_pos, avg_labels_list, alpha=0.7)
        plt.title('不同数据类型的平均可通行性')
        plt.xlabel('数据类型')
        plt.ylabel('平均可通行性标签')
        plt.xticks(x_pos, [t.replace('_', '\n') for t in types], rotation=45, ha='right')
        
        # 4. 弱标签质量评估
        plt.subplot(2, 3, 5)
        
        # 计算不同奖励区间的标签分布
        high_reward_mask = np.array(rewards) > 2.5
        med_reward_mask = (np.array(rewards) >= 1.5) & (np.array(rewards) <= 2.5)
        low_reward_mask = np.array(rewards) < 1.5
        
        high_labels = np.array(weak_labels)[high_reward_mask]
        med_labels = np.array(weak_labels)[med_reward_mask]
        low_labels = np.array(weak_labels)[low_reward_mask]
        
        plt.boxplot([low_labels, med_labels, high_labels], 
                   labels=['低奖励\n(<1.5)', '中奖励\n(1.5-2.5)', '高奖励\n(>2.5)'])
        plt.title('不同奖励区间的标签分布')
        plt.ylabel('可通行性标签')
        
        # 5. 标签区分度分析
        plt.subplot(2, 3, 6)
        
        label_separation = {
            '高-低': np.mean(high_labels) - np.mean(low_labels) if len(high_labels) > 0 and len(low_labels) > 0 else 0,
            '高-中': np.mean(high_labels) - np.mean(med_labels) if len(high_labels) > 0 and len(med_labels) > 0 else 0,
            '中-低': np.mean(med_labels) - np.mean(low_labels) if len(med_labels) > 0 and len(low_labels) > 0 else 0,
        }
        
        separations = list(label_separation.values())
        labels_sep = list(label_separation.keys())
        
        colors = ['green' if s > 0.1 else 'orange' if s > 0.05 else 'red' for s in separations]
        plt.bar(labels_sep, separations, color=colors, alpha=0.7)
        plt.title('标签区分度分析')
        plt.ylabel('标签差异')
        plt.axhline(y=0.1, color='green', linestyle='--', alpha=0.5, label='良好阈值')
        plt.axhline(y=0.05, color='orange', linestyle='--', alpha=0.5, label='可接受阈值')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('step1_weak_label_analysis.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        # 保存分析结果
        analysis_results = {
            'total_samples': len(rewards),
            'reward_range': [float(np.min(rewards)), float(np.max(rewards))],
            'reward_mean': float(np.mean(rewards)),
            'reward_std': float(np.std(rewards)),
            'label_range': [float(np.min(weak_labels)), float(np.max(weak_labels))],
            'label_mean': float(np.mean(weak_labels)),
            'label_std': float(np.std(weak_labels)),
            'type_analysis': {k: float(v) for k, v in type_avg_labels.items()},
            'separation_analysis': {k: float(v) for k, v in label_separation.items()},
            'quality_score': float(np.mean(list(label_separation.values())))
        }
        
        return analysis_results
    
    def test_segmentation_training(self, all_data):
        """测试分割网络训练"""
        print("\n🧠 测试分割网络训练...")
        
        # 创建简单的训练数据
        train_images = []
        train_rewards = []
        
        # 从每个episode取一些样本
        for episode in all_data[:5]:  # 只用前5个episodes测试
            images = episode['images'][:10]  # 每个episode取10帧
            rewards = episode['rewards'][:10]
            
            train_images.extend(images)
            train_rewards.extend(rewards)
        
        if len(train_images) == 0:
            print("❌ 没有可用的训练数据")
            return None
        
        train_images = np.array(train_images).astype(np.float32) / 255.0
        train_rewards = np.array(train_rewards).astype(np.float32)
        
        print(f"   训练数据: {len(train_images)}张图像")
        print(f"   奖励范围: [{train_rewards.min():.3f}, {train_rewards.max():.3f}]")
        
        # 创建训练器
        trainer = TaskAwarePassabilityTrainer(
            feature_channels=32,
            downsample_ratio=8,
            learning_rate=1e-4,
            batch_size=4
        )
        
        # 初始化
        rng_key = jax.random.PRNGKey(42)
        trainer.initialize(rng_key)
        
        # 简单训练循环
        print("   开始训练...")
        training_losses = []
        
        for epoch in range(10):  # 只训练10个epoch测试
            epoch_losses = []
            
            # 简单的批次处理
            for i in range(0, len(train_images), trainer.batch_size):
                batch_images = train_images[i:i+trainer.batch_size]
                batch_rewards = train_rewards[i:i+trainer.batch_size]
                
                if len(batch_images) < trainer.batch_size:
                    continue
                
                # 训练步骤
                try:
                    loss, metrics = trainer.train_step(batch_images, batch_rewards)
                    epoch_losses.append(float(loss))
                except Exception as e:
                    print(f"     训练错误: {e}")
                    continue
            
            if epoch_losses:
                avg_loss = np.mean(epoch_losses)
                training_losses.append(avg_loss)
                print(f"   Epoch {epoch+1}: Loss = {avg_loss:.6f}")
        
        # 测试推理
        print("   测试推理...")
        test_image = train_images[0:1]  # 取第一张图像
        test_reward = train_rewards[0:1]
        
        try:
            outputs = trainer.infer(test_image)
            
            print(f"   推理结果:")
            print(f"     概率图形状: {outputs['prob'].shape}")
            print(f"     特征图形状: {outputs['feat'].shape}")
            print(f"     评分: {outputs['score'][0]:.6f}")
            print(f"     实际奖励: {test_reward[0]:.6f}")
            
            # 可视化结果
            fig, axes = plt.subplots(1, 3, figsize=(12, 4))
            
            # 原始图像
            axes[0].imshow(test_image[0])
            axes[0].set_title('原始图像')
            axes[0].axis('off')
            
            # 概率图
            prob_map = outputs['prob'][0, :, :, 0]
            im1 = axes[1].imshow(prob_map, cmap='RdYlGn', vmin=0, vmax=1)
            axes[1].set_title(f'可通行性概率\n(评分: {outputs["score"][0]:.3f})')
            axes[1].axis('off')
            plt.colorbar(im1, ax=axes[1], fraction=0.046, pad=0.04)
            
            # 训练曲线
            if training_losses:
                axes[2].plot(training_losses, 'b-', linewidth=2)
                axes[2].set_title('训练损失曲线')
                axes[2].set_xlabel('Epoch')
                axes[2].set_ylabel('Loss')
                axes[2].grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig('step1_segmentation_test.png', dpi=150, bbox_inches='tight')
            plt.show()
            
            return {
                'training_successful': True,
                'final_loss': training_losses[-1] if training_losses else None,
                'inference_score': float(outputs['score'][0]),
                'actual_reward': float(test_reward[0]),
                'prob_map_stats': {
                    'min': float(prob_map.min()),
                    'max': float(prob_map.max()),
                    'mean': float(prob_map.mean())
                }
            }
            
        except Exception as e:
            print(f"   ❌ 推理失败: {e}")
            return {'training_successful': False, 'error': str(e)}
    
    def run_validation(self):
        """运行完整验证"""
        print("🎯 开始验证第一步：弱标签生成和分割网络训练")
        print("=" * 60)
        
        # 1. 加载数据
        all_data, data_stats = self.load_collected_data()
        
        if len(all_data) == 0:
            print("❌ 没有找到有效数据，请先运行数据收集")
            return
        
        # 2. 分析弱标签
        label_analysis = self.analyze_weak_labels(all_data, data_stats)
        
        # 3. 测试分割网络训练
        training_results = self.test_segmentation_training(all_data)
        
        # 4. 生成总结报告
        self.generate_report(data_stats, label_analysis, training_results)
    
    def generate_report(self, data_stats, label_analysis, training_results):
        """生成验证报告"""
        print("\n📋 第一步验证报告")
        print("=" * 40)
        
        print(f"📊 数据统计:")
        print(f"   总episodes: {data_stats['total_episodes']}")
        print(f"   总帧数: {data_stats['total_frames']}")
        print(f"   奖励范围: [{label_analysis['reward_range'][0]:.3f}, {label_analysis['reward_range'][1]:.3f}]")
        
        print(f"\n🏷️  弱标签质量:")
        print(f"   标签范围: [{label_analysis['label_range'][0]:.3f}, {label_analysis['label_range'][1]:.3f}]")
        print(f"   质量评分: {label_analysis['quality_score']:.3f}")
        
        for sep_type, sep_value in label_analysis['separation_analysis'].items():
            status = "✅" if sep_value > 0.1 else "⚠️" if sep_value > 0.05 else "❌"
            print(f"   {sep_type}区分度: {sep_value:.3f} {status}")
        
        print(f"\n🧠 分割网络训练:")
        if training_results and training_results.get('training_successful'):
            print(f"   训练状态: ✅ 成功")
            print(f"   最终损失: {training_results.get('final_loss', 'N/A'):.6f}")
            print(f"   推理评分: {training_results.get('inference_score', 'N/A'):.6f}")
            print(f"   实际奖励: {training_results.get('actual_reward', 'N/A'):.6f}")
        else:
            print(f"   训练状态: ❌ 失败")
            if training_results:
                print(f"   错误信息: {training_results.get('error', 'Unknown')}")
        
        # 保存完整报告
        report = {
            'data_stats': data_stats,
            'label_analysis': label_analysis,
            'training_results': training_results,
            'timestamp': str(np.datetime64('now'))
        }
        
        with open('step1_validation_report.pkl', 'wb') as f:
            pickle.dump(report, f)
        
        print(f"\n💾 完整报告已保存到: step1_validation_report.pkl")
        print(f"📈 可视化结果已保存到: step1_weak_label_analysis.png, step1_segmentation_test.png")


def main():
    """主函数"""
    validator = Step1Validator()
    validator.run_validation()


if __name__ == "__main__":
    main()
