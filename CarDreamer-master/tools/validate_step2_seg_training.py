"""
验证第二步：分割网络训练
验证TaskAwarePassabilitySegNet能否从弱标签中学习有效的可通行性分割
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import jax
import jax.numpy as jnp
from pathlib import Path
import cv2
import pickle
import time
from typing import Dict, List, Tuple

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from models.passability_seg_sota import TaskAwarePassabilitySegNet
from dreamerv3 import ninjax as nj


class SimpleStep2Validator:
    """简化的第二步验证器"""
    
    def __init__(self, data_dir="data/episodes_diverse"):
        self.data_dir = Path(data_dir)
        self.model = None
        self.params = None
        
    def load_sample_data(self, max_samples=50):
        """加载样本数据"""
        print("🔍 加载训练数据...")
        
        all_images = []
        all_rewards = []
        
        # 找到第一个有效的数据目录
        for data_type_dir in self.data_dir.iterdir():
            if not data_type_dir.is_dir():
                continue
                
            print(f"  📁 处理: {data_type_dir.name}")
            
            for ep_dir in data_type_dir.iterdir():
                if not ep_dir.is_dir() or not ep_dir.name.startswith('ep_'):
                    continue
                    
                meta_file = ep_dir / 'meta.npz'
                rgb_dir = ep_dir / 'rgb'
                
                if meta_file.exists() and rgb_dir.exists():
                    try:
                        # 加载元数据
                        meta_data = np.load(meta_file)
                        rewards = meta_data['rewards']
                        
                        # 加载图像
                        rgb_files = []
                        for ext in ['*.png', '*.jpg', '*.jpeg']:
                            rgb_files.extend(list(rgb_dir.glob(ext)))
                        rgb_files = sorted(rgb_files)[:20]  # 每个episode最多20张
                        
                        for i, rgb_file in enumerate(rgb_files):
                            if len(all_images) >= max_samples:
                                break
                                
                            img = cv2.imread(str(rgb_file))
                            if img is not None:
                                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                                img = cv2.resize(img, (128, 128))
                                
                                all_images.append(img)
                                reward_idx = min(i, len(rewards)-1)
                                all_rewards.append(rewards[reward_idx])
                        
                        if len(all_images) >= max_samples:
                            break
                            
                    except Exception as e:
                        print(f"    ⚠️  加载失败: {e}")
                        continue
            
            if len(all_images) >= max_samples:
                break
        
        if len(all_images) == 0:
            print("❌ 没有找到有效数据")
            return None, None
        
        images = np.array(all_images).astype(np.float32) / 255.0
        rewards = np.array(all_rewards).astype(np.float32)
        
        print(f"✅ 加载了 {len(images)} 个样本")
        print(f"   奖励范围: [{rewards.min():.3f}, {rewards.max():.3f}]")
        
        return images, rewards
    
    def create_model(self):
        """创建分割模型"""
        print("🧠 创建分割模型...")
        
        self.model = TaskAwarePassabilitySegNet(
            feature_channels=32,  # 减少通道数加速训练
            downsample_ratio=8,
            use_attention=True,
            use_contrastive=False,  # 简化训练
            use_temporal=False,
            name='step2_seg'
        )
        
        print("✅ 模型创建成功")
    
    def initialize_model(self, sample_image):
        """初始化模型参数"""
        print("🔧 初始化模型参数...")
        
        key = jax.random.PRNGKey(42)
        test_input = jnp.expand_dims(jnp.array(sample_image), axis=0)
        
        def init_fn(x):
            return self.model(x, task_context=None, prev_features=None)
        
        pure_init = nj.pure(init_fn)
        outputs, params = pure_init({}, key, test_input)
        
        self.params = params
        
        print("✅ 模型初始化完成")
        print(f"   输出键: {list(outputs.keys())}")
        
        return outputs
    
    def simple_training_loop(self, images, rewards, num_epochs=20):
        """简化的训练循环"""
        print(f"🚀 开始简化训练 ({num_epochs} epochs)...")
        
        # 生成弱标签
        reward_clipped = np.clip(rewards, -5.0, 5.0)
        weak_labels = (reward_clipped + 5.0) / 10.0
        
        # 创建优化器
        import optax
        optimizer = optax.adam(1e-4)
        opt_state = optimizer.init(self.params)
        
        # 定义损失函数
        def loss_fn(params, batch_images, batch_labels):
            def forward_fn(x):
                return self.model(x, task_context=None, prev_features=None)
            
            pure_forward = nj.pure(forward_fn)
            outputs, _ = pure_forward(params, jax.random.PRNGKey(0), batch_images)
            
            # 简单的MSE损失
            scores = outputs['score']
            loss = jnp.mean((scores - batch_labels) ** 2)
            
            return loss, {'loss': loss, 'scores': scores}
        
        # JIT编译
        grad_fn = jax.jit(jax.value_and_grad(loss_fn, has_aux=True))
        
        training_losses = []
        batch_size = 4
        
        for epoch in range(num_epochs):
            epoch_losses = []
            
            # 随机打乱数据
            indices = np.random.permutation(len(images))
            
            for i in range(0, len(images), batch_size):
                batch_indices = indices[i:i+batch_size]
                if len(batch_indices) < batch_size:
                    continue
                
                batch_images = jnp.array(images[batch_indices])
                batch_labels = jnp.array(weak_labels[batch_indices])
                
                try:
                    # 计算梯度
                    (loss, aux), grads = grad_fn(self.params, batch_images, batch_labels)
                    
                    # 更新参数
                    updates, opt_state = optimizer.update(grads, opt_state)
                    self.params = optax.apply_updates(self.params, updates)
                    
                    epoch_losses.append(float(loss))
                    
                except Exception as e:
                    print(f"    批次训练失败: {e}")
                    continue
            
            if epoch_losses:
                avg_loss = np.mean(epoch_losses)
                training_losses.append(avg_loss)
                
                if (epoch + 1) % 5 == 0 or epoch < 5:
                    print(f"    Epoch {epoch+1:3d}: Loss = {avg_loss:.6f}")
        
        print(f"✅ 训练完成！最终损失: {training_losses[-1]:.6f}")
        return training_losses
    
    def evaluate_model(self, images, rewards):
        """评估模型"""
        print("🔍 评估训练后的模型...")
        
        # 生成弱标签
        reward_clipped = np.clip(rewards, -5.0, 5.0)
        weak_labels = (reward_clipped + 5.0) / 10.0
        
        # 批量推理
        batch_size = 8
        all_scores = []
        all_prob_maps = []
        
        for i in range(0, len(images), batch_size):
            batch_images = images[i:i+batch_size]
            batch_images_jax = jnp.array(batch_images)
            
            try:
                def forward_fn(x):
                    return self.model(x, task_context=None, prev_features=None)
                
                pure_forward = nj.pure(forward_fn)
                outputs, _ = pure_forward(self.params, jax.random.PRNGKey(0), batch_images_jax)
                
                all_scores.extend(outputs['score'])
                all_prob_maps.extend(outputs['prob'])
                
            except Exception as e:
                print(f"    推理失败: {e}")
                continue
        
        if not all_scores:
            print("❌ 推理失败")
            return None
        
        scores = np.array(all_scores)
        prob_maps = np.array(all_prob_maps)
        
        # 计算相关性
        correlation = np.corrcoef(scores, weak_labels)[0, 1]
        
        print(f"✅ 评估完成:")
        print(f"   相关性: {correlation:.3f}")
        print(f"   评分范围: [{scores.min():.3f}, {scores.max():.3f}]")
        
        return {
            'correlation': correlation,
            'scores': scores,
            'prob_maps': prob_maps,
            'weak_labels': weak_labels
        }
    
    def visualize_results(self, images, rewards, results, training_losses):
        """可视化结果"""
        print("🎨 生成可视化...")
        
        scores = results['scores']
        prob_maps = results['prob_maps']
        weak_labels = results['weak_labels']
        
        plt.figure(figsize=(15, 10))
        
        # 训练曲线
        plt.subplot(2, 3, 1)
        plt.plot(training_losses, 'b-', linewidth=2)
        plt.title('训练损失曲线')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.grid(True, alpha=0.3)
        
        # 相关性散点图
        plt.subplot(2, 3, 2)
        plt.scatter(weak_labels, scores, alpha=0.6, s=30)
        plt.plot([0, 1], [0, 1], 'r--', alpha=0.8, label='Perfect')
        plt.xlabel('弱标签')
        plt.ylabel('网络评分')
        plt.title(f'相关性: {results["correlation"]:.3f}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 分布对比
        plt.subplot(2, 3, 3)
        plt.hist(weak_labels, bins=15, alpha=0.5, label='弱标签', color='blue')
        plt.hist(scores, bins=15, alpha=0.5, label='网络评分', color='orange')
        plt.title('分布对比')
        plt.xlabel('评分值')
        plt.ylabel('频次')
        plt.legend()
        
        # 样本可视化
        n_samples = min(6, len(images))
        indices = np.linspace(0, len(images)-1, n_samples, dtype=int)
        
        for i, idx in enumerate(indices):
            # 原始图像
            plt.subplot(2, 3, 4)
            if i == 0:  # 只显示第一个样本
                plt.imshow(images[idx])
                plt.title(f'样本图像\nReward: {rewards[idx]:.2f}')
                plt.axis('off')
            
            # 概率图
            plt.subplot(2, 3, 5)
            if i == 0:  # 只显示第一个样本
                prob_map = prob_maps[idx, :, :, 0]
                plt.imshow(prob_map, cmap='RdYlGn', vmin=0, vmax=1)
                plt.title(f'可通行性概率图\nScore: {scores[idx]:.3f}')
                plt.axis('off')
                plt.colorbar(fraction=0.046, pad=0.04)
        
        # 评分对比
        plt.subplot(2, 3, 6)
        sample_indices = indices[:4]  # 取前4个样本
        x_pos = np.arange(len(sample_indices))
        width = 0.35
        
        plt.bar(x_pos - width/2, weak_labels[sample_indices], width, 
               label='弱标签', alpha=0.7, color='blue')
        plt.bar(x_pos + width/2, scores[sample_indices], width, 
               label='网络评分', alpha=0.7, color='orange')
        plt.title('样本对比')
        plt.xlabel('样本')
        plt.ylabel('评分')
        plt.legend()
        plt.xticks(x_pos, [f'S{i+1}' for i in range(len(sample_indices))])
        
        plt.tight_layout()
        plt.savefig('step2_simple_training_results.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✅ 可视化完成")
    
    def run_validation(self):
        """运行验证"""
        print("🎯 开始验证第二步：分割网络训练")
        print("=" * 50)
        
        # 1. 加载数据
        images, rewards = self.load_sample_data(max_samples=40)
        if images is None:
            return
        
        # 2. 创建和初始化模型
        self.create_model()
        initial_outputs = self.initialize_model(images[0])
        
        # 3. 简化训练
        training_losses = self.simple_training_loop(images, rewards, num_epochs=15)
        
        # 4. 评估模型
        results = self.evaluate_model(images, rewards)
        if results is None:
            return
        
        # 5. 可视化结果
        self.visualize_results(images, rewards, results, training_losses)
        
        # 6. 生成报告
        print("\n📋 第二步验证报告")
        print("=" * 30)
        print(f"✅ 训练成功完成")
        print(f"   最终损失: {training_losses[-1]:.6f}")
        print(f"   相关性: {results['correlation']:.3f}")
        
        if results['correlation'] > 0.5:
            print("🎉 验证成功！分割网络能够从弱标签中学习")
        elif results['correlation'] > 0.3:
            print("⚠️  部分成功，需要进一步优化")
        else:
            print("❌ 验证失败，需要检查训练策略")
        
        # 保存结果
        report = {
            'training_losses': training_losses,
            'correlation': results['correlation'],
            'final_loss': training_losses[-1],
            'timestamp': str(np.datetime64('now'))
        }
        
        with open('step2_simple_validation_report.pkl', 'wb') as f:
            pickle.dump(report, f)
        
        print(f"\n💾 报告已保存: step2_simple_validation_report.pkl")


def main():
    """主函数"""
    validator = SimpleStep2Validator()
    validator.run_validation()


if __name__ == "__main__":
    main()
