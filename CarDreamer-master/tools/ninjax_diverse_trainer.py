"""
基于ninjax的多样化数据任务感知分割训练器
专门针对收集的多样化数据进行训练，使用CarDreamer的ninjax框架

数据类型：
1. 正样本：expert_* - 使用匹配checkpoint在对应任务中采集的标准数据
2. 失败样本：cross_* - 任务和checkpoint不匹配的失败样本
3. 噪声样本：noisy_* - 在动作中引入噪声的样本
4. 困难样本：hard_* - 复杂场景如left-hard、traffic_light等
"""

import os
import sys
import pickle
import time
import numpy as np
import jax
import jax.numpy as jnp
from jax import random
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import matplotlib.pyplot as plt

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from dreamerv3 import ninjax as nj
from dreamerv3 import nets

f32 = jnp.float32


class TaskAwareSegNet(nj.Module):
    """基于ninjax的任务感知分割网络"""
    
    def __init__(self, feature_dim=256, task_context_dim=64, name='task_aware_seg'):
        self.feature_dim = feature_dim
        self.task_context_dim = task_context_dim
        
    def __call__(self, rgb_input, task_context=None):
        """
        前向传播
        Args:
            rgb_input: (B, H, W, 3) RGB图像
            task_context: (B, task_context_dim) 任务上下文
        Returns:
            Dict包含分割结果和特征
        """
        B, H, W, C = rgb_input.shape
        
        # 简化的编码器：使用线性层模拟卷积
        # 将图像展平
        x_flat = rgb_input.reshape(B, -1)  # (B, H*W*3)
        
        # 编码器层
        x = nets.Linear(512, name='encoder1')(x_flat)
        x = jax.nn.relu(x)
        x = nets.Linear(256, name='encoder2')(x)
        x = jax.nn.relu(x)
        x = nets.Linear(self.feature_dim, name='encoder3')(x)
        global_feat = jax.nn.relu(x)  # (B, feature_dim)
        
        # 任务上下文融合
        if task_context is not None:
            context_proj = nets.Linear(self.feature_dim, name='context_proj')(task_context)
            global_feat = global_feat + 0.3 * context_proj

        # 分割头：生成可通行性概率图
        seg_feat = nets.Linear(H//4 * W//4, name='seg_head')(global_feat)  # 1/4尺度
        seg_feat = seg_feat.reshape(B, H//4, W//4, 1)
        passability_prob = jax.nn.sigmoid(seg_feat)

        # 任务感知评分
        task_score = nets.Linear(1, name='task_score_head')(global_feat)
        task_score = task_score.squeeze(-1)  # (B,)

        # 特征嵌入用于DreamerV3
        embed_feat = nets.Linear(128, name='embed_head')(global_feat)
        
        return {
            'passability_prob': passability_prob,  # (B, H/4, W/4, 1)
            'task_score': task_score,              # (B,)
            'embed_feat': embed_feat,              # (B, 128)
            'global_feat': global_feat,            # (B, feature_dim)
        }


class NinjaxDiverseTrainer:
    """基于ninjax的多样化数据训练器"""
    
    def __init__(self, config):
        self.config = config
        self.data_dir = Path(config.get('data_dir', 'data/episodes_diverse'))
        self.output_dir = Path(config.get('output_dir', 'training_output_diverse'))
        self.output_dir.mkdir(exist_ok=True)
        
        # 模型配置
        self.feature_dim = config.get('feature_dim', 256)
        self.task_context_dim = config.get('task_context_dim', 64)
        
        # 训练配置
        self.batch_size = config.get('batch_size', 4)  # 减小批次大小
        self.learning_rate = config.get('learning_rate', 1e-4)
        self.num_epochs = config.get('num_epochs', 20)
        
        # 初始化模型
        self.setup_model()
        
        # 数据统计
        self.data_stats = {}
        
        print(f"🚀 Ninjax多样化数据训练器初始化完成")
        print(f"   数据目录: {self.data_dir}")
        print(f"   输出目录: {self.output_dir}")
    
    def setup_model(self):
        """初始化模型"""
        # 定义模型函数
        def model_fn(rgb_input, task_context=None):
            model = TaskAwareSegNet(
                feature_dim=self.feature_dim,
                task_context_dim=self.task_context_dim,
                name='task_aware_seg'
            )
            return model(rgb_input, task_context)

        # 使用ninjax的pure函数包装
        self.pure_model = nj.pure(model_fn)
        
        # 初始化参数
        rng = random.PRNGKey(42)
        dummy_rgb = jnp.ones((1, 224, 224, 3))
        dummy_context = jnp.ones((1, self.task_context_dim))
        
        # 使用pure函数初始化，返回(output, state)
        _, self.state = self.pure_model({}, rng, dummy_rgb, dummy_context)
        
        print("✅ Ninjax模型初始化完成")
    
    def load_episode_data(self, episode_path: Path) -> Optional[Dict[str, Any]]:
        """加载单个episode数据"""
        try:
            # 检查meta.npz文件
            meta_file = episode_path / 'meta.npz'
            if not meta_file.exists():
                return None

            # 加载meta数据
            meta_data = np.load(meta_file)
            actions = meta_data['actions']  # (T, 15)
            rewards = meta_data['rewards']  # (T,)

            # 检查RGB图像目录
            rgb_dir = episode_path / 'rgb'
            if not rgb_dir.exists():
                return None

            # 获取第一帧图像
            rgb_files = sorted(list(rgb_dir.glob('frame_*.jpg')))
            if not rgb_files:
                return None

            # 加载第一帧图像
            from PIL import Image
            rgb_image = np.array(Image.open(rgb_files[0]))
            
            # 确保图像格式正确
            if isinstance(rgb_image, np.ndarray):
                if len(rgb_image.shape) == 3 and rgb_image.shape[-1] == 3:
                    # 归一化到[0,1]
                    if rgb_image.max() > 1.0:
                        rgb_image = rgb_image.astype(np.float32) / 255.0
                    
                    # 调整到224x224
                    if rgb_image.shape[:2] != (224, 224):
                        from scipy.ndimage import zoom
                        h, w = rgb_image.shape[:2]
                        zoom_factors = (224/h, 224/w, 1)
                        rgb_image = zoom(rgb_image, zoom_factors, order=1)
                else:
                    return None
            else:
                return None
            
            # 提取第一步的动作和奖励
            action = actions[0][:3].astype(np.float32)  # 取前3个动作维度
            reward = float(rewards[0])
            
            return {
                'rgb_image': rgb_image,
                'action': action,
                'reward': reward,
                'episode_path': str(episode_path)
            }
            
        except Exception as e:
            print(f"❌ 加载episode数据失败 {episode_path}: {e}")
            return None
    
    def analyze_data_distribution(self):
        """分析数据分布"""
        print("📊 分析数据分布...")
        
        data_types = {
            'expert': [],      # 正样本
            'cross': [],       # 失败样本  
            'noisy': [],       # 噪声样本
            'hard': []         # 困难样本
        }
        
        # 遍历数据目录
        for data_folder in self.data_dir.iterdir():
            if not data_folder.is_dir():
                continue
                
            folder_name = data_folder.name
            
            # 分类数据类型
            if folder_name.startswith('expert_'):
                data_type = 'expert'
            elif folder_name.startswith('cross_'):
                data_type = 'cross'
            elif folder_name.startswith('noisy_'):
                data_type = 'noisy'
            elif folder_name.startswith('hard_'):
                data_type = 'hard'
            else:
                continue
            
            # 统计episode数量
            episodes = list(data_folder.glob('ep_*'))
            data_types[data_type].extend(episodes)
        
        # 输出统计信息
        total_episodes = sum(len(episodes) for episodes in data_types.values())
        print(f"   总计: {total_episodes} episodes")
        for data_type, episodes in data_types.items():
            print(f"   {data_type:>8}: {len(episodes):>3} episodes")
        
        self.data_stats = data_types
        return data_types
    
    def create_training_data(self, data_types: Dict[str, List]) -> List[Dict]:
        """创建训练数据"""
        print("🔄 创建训练数据...")
        
        training_data = []
        
        for data_type, episodes in data_types.items():
            print(f"   处理 {data_type} 数据: {len(episodes)} episodes")
            
            for episode_path in episodes:
                episode_data = self.load_episode_data(episode_path)
                if episode_data is None:
                    continue
                
                episode_data['data_type'] = data_type
                training_data.append(episode_data)
        
        print(f"✅ 成功加载 {len(training_data)} 个训练样本")
        return training_data
    
    def generate_pseudo_labels(self, data_type: str, image_shape: Tuple[int, int], reward: float = 0.0) -> jnp.ndarray:
        """基于数据类型和奖励生成更智能的伪标签"""
        h, w = image_shape

        # 奖励归一化到[0,1]范围
        normalized_reward = np.clip((reward + 1.0) / 6.0, 0.0, 1.0)  # 假设奖励范围[-1, 5]

        # 根据数据类型和奖励生成伪标签
        if data_type == 'expert':
            # 正样本：基于奖励的高可通行性
            base_prob = 0.6 + 0.3 * normalized_reward  # [0.6, 0.9]
            noise_level = 0.05
        elif data_type == 'cross':
            # 失败样本：基于奖励的低可通行性
            base_prob = 0.4 - 0.2 * normalized_reward  # [0.2, 0.4]
            noise_level = 0.1
        elif data_type == 'noisy':
            # 噪声样本：中等可通行性，基于奖励调整
            base_prob = 0.3 + 0.4 * normalized_reward  # [0.3, 0.7]
            noise_level = 0.15
        elif data_type == 'hard':
            # 困难样本：低到中等可通行性
            base_prob = 0.2 + 0.3 * normalized_reward  # [0.2, 0.5]
            noise_level = 0.1
        else:
            base_prob = 0.5
            noise_level = 0.2

        # 生成空间变化的伪标签
        label = np.full((h//4, w//4, 1), base_prob, dtype=np.float32)

        # 添加空间结构：中心区域更可通行
        center_h, center_w = h//8, w//8
        y, x = np.ogrid[:h//4, :w//4]
        center_mask = ((y - center_h)**2 + (x - center_w)**2) < (min(center_h, center_w)**2)
        label[center_mask] = np.clip(label[center_mask] + 0.1, 0.0, 1.0)

        # 添加噪声
        noise = np.random.normal(0, noise_level, label.shape).astype(np.float32)
        label = np.clip(label + noise, 0.0, 1.0)

        return jnp.array(label)
    
    def compute_loss(self, pred_outputs, pseudo_labels, rewards, data_types):
        """计算改进的损失函数"""
        pred_prob = pred_outputs['passability_prob']
        task_scores = pred_outputs['task_score']

        # 1. 分割损失 (BCE) - 加权版本
        bce_loss = -jnp.mean(
            pseudo_labels * jnp.log(pred_prob + 1e-8) +
            (1 - pseudo_labels) * jnp.log(1 - pred_prob + 1e-8)
        )

        # 2. 改进的任务感知损失 - 归一化奖励
        normalized_rewards = jnp.clip((rewards + 1.0) / 6.0, 0.0, 1.0)
        task_loss = jnp.mean((task_scores - normalized_rewards) ** 2)

        # 3. 数据类型对比损失
        expert_mask = jnp.array([dt == 'expert' for dt in data_types])
        cross_mask = jnp.array([dt == 'cross' for dt in data_types])

        # 专家数据应该有更高的任务评分
        expert_loss = jnp.mean(jnp.where(expert_mask,
                                       jnp.maximum(0, 0.7 - task_scores), 0.0))

        # 失败数据应该有更低的任务评分
        cross_loss = jnp.mean(jnp.where(cross_mask,
                                      jnp.maximum(0, task_scores - 0.3), 0.0))

        # 4. 正则化损失 - 防止过拟合
        reg_loss = 0.01 * jnp.mean(task_scores ** 2)

        total_loss = bce_loss + 1.0 * task_loss + 0.5 * expert_loss + 0.5 * cross_loss + reg_loss

        return total_loss, {
            'bce_loss': bce_loss,
            'task_loss': task_loss,
            'expert_loss': expert_loss,
            'cross_loss': cross_loss,
            'reg_loss': reg_loss,
            'total_loss': total_loss
        }
    
    def train_step(self, state, batch_data):
        """单步训练"""
        rng = random.PRNGKey(int(time.time() * 1000) % 2**32)
        
        def loss_fn(state):
            # 准备输入
            rgb_images = jnp.array([item['rgb_image'] for item in batch_data])
            task_context = jnp.zeros((len(batch_data), self.task_context_dim))

            # 前向传播
            pred_outputs, new_state = self.pure_model(state, rng, rgb_images, task_context)

            # 批量生成伪标签
            pseudo_labels = []
            rewards = []
            data_types = []

            for item in batch_data:
                pseudo_label = self.generate_pseudo_labels(
                    item['data_type'],
                    item['rgb_image'].shape[:2],
                    item['reward']  # 传入奖励值
                )
                pseudo_labels.append(pseudo_label)
                rewards.append(item['reward'])
                data_types.append(item['data_type'])

            # 转换为JAX数组
            pseudo_labels = jnp.array(pseudo_labels)
            rewards = jnp.array(rewards)

            # 计算批量损失
            total_loss, loss_dict = self.compute_loss(
                pred_outputs,
                pseudo_labels,
                rewards,
                data_types
            )

            return total_loss, (new_state, pred_outputs, loss_dict)
        
        # 计算梯度
        (loss, (new_state, pred_outputs, loss_dict)), grads = jax.value_and_grad(
            loss_fn, has_aux=True
        )(state)
        
        # 简单的梯度更新 (SGD)
        lr = self.learning_rate
        new_state = jax.tree_map(lambda p, g: p - lr * g, state, grads)
        
        return new_state, loss, loss_dict, pred_outputs
    
    def train(self):
        """主训练循环"""
        print("🎯 开始训练...")
        
        # 分析数据分布
        data_types = self.analyze_data_distribution()
        
        # 创建训练数据
        training_data = self.create_training_data(data_types)
        
        if not training_data:
            print("❌ 没有找到有效的训练数据")
            return
        
        # 训练历史
        training_history = {
            'losses': [],
            'data_type_performance': {dt: [] for dt in data_types.keys()}
        }
        
        # 训练循环
        step = 0
        for epoch in range(self.num_epochs):
            print(f"\n📅 Epoch {epoch + 1}/{self.num_epochs}")
            
            # 随机打乱数据
            np.random.shuffle(training_data)
            
            # 创建批次
            epoch_losses = []
            for i in range(0, len(training_data), self.batch_size):
                batch_data = training_data[i:i+self.batch_size]
                
                # 训练步骤
                self.state, loss, loss_dict, pred_outputs = self.train_step(
                    self.state, batch_data
                )
                
                epoch_losses.append(float(loss))
                
                # 记录性能
                for item, pred in zip(batch_data, pred_outputs['task_score']):
                    data_type = item['data_type']
                    training_history['data_type_performance'][data_type].append(float(pred))
                
                # 日志输出
                if step % 10 == 0:
                    print(f"  Step {step:3d} | Loss: {loss:.4f} | "
                          f"BCE: {loss_dict['bce_loss']:.4f} | "
                          f"Task: {loss_dict['task_loss']:.4f}")
                
                step += 1
            
            # Epoch总结
            avg_loss = np.mean(epoch_losses)
            training_history['losses'].append(avg_loss)
            
            print(f"  Epoch {epoch + 1} 平均损失: {avg_loss:.4f}")
            
            # 保存检查点
            if (epoch + 1) % 10 == 0:
                self.save_checkpoint(f'checkpoint_epoch_{epoch + 1}.pkl')
        
        # 保存最终模型
        self.save_checkpoint('final_diverse_model.pkl')
        self.save_training_history(training_history)

        print("🎉 训练完成！")
        return training_history
    
    def save_checkpoint(self, filename):
        """保存检查点"""
        checkpoint = {
            'state': self.state,
            'config': self.config,
            'data_stats': self.data_stats
        }
        
        checkpoint_path = self.output_dir / filename
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint, f)
        
        print(f"💾 检查点已保存: {checkpoint_path}")
    
    def save_training_history(self, history):
        """保存训练历史"""
        history_path = self.output_dir / 'training_history.pkl'
        with open(history_path, 'wb') as f:
            pickle.dump(history, f)
        
        print(f"📈 训练历史已保存: {history_path}")


def create_ninjax_config():
    """创建ninjax训练配置"""
    return {
        'data_dir': 'data/episodes_diverse',
        'output_dir': 'training_output_diverse',
        'feature_dim': 256,
        'task_context_dim': 64,
        'batch_size': 4,
        'learning_rate': 1e-4,
        'num_epochs': 20,
    }


if __name__ == '__main__':
    config = create_ninjax_config()
    trainer = NinjaxDiverseTrainer(config)
    trainer.train()
