"""
简化的第一步测试：验证弱标签生成和基础分割网络
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import jax
import jax.numpy as jnp
from pathlib import Path
import cv2

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from models.passability_seg_sota import TaskAwarePassabilitySegNet
from dreamerv3 import ninjax as nj


def load_sample_data():
    """加载样本数据"""
    data_dir = Path("data/episodes_diverse")
    
    # 找到第一个有效的episode
    for data_type_dir in data_dir.iterdir():
        if not data_type_dir.is_dir():
            continue
            
        for ep_dir in data_type_dir.iterdir():
            if not ep_dir.is_dir() or not ep_dir.name.startswith('ep_'):
                continue
                
            meta_file = ep_dir / 'meta.npz'
            rgb_dir = ep_dir / 'rgb'
            
            if meta_file.exists() and rgb_dir.exists():
                try:
                    # 加载元数据
                    meta_data = np.load(meta_file)
                    rewards = meta_data['rewards'][:10]  # 只取前10帧
                    
                    # 加载图像
                    rgb_files = []
                    for ext in ['*.png', '*.jpg', '*.jpeg']:
                        rgb_files.extend(list(rgb_dir.glob(ext)))
                    rgb_files = sorted(rgb_files)[:10]
                    
                    images = []
                    for rgb_file in rgb_files:
                        img = cv2.imread(str(rgb_file))
                        if img is not None:
                            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                            img = cv2.resize(img, (128, 128))
                            images.append(img)
                    
                    if len(images) > 0:
                        return np.array(images).astype(np.float32) / 255.0, rewards[:len(images)]
                        
                except Exception as e:
                    continue
    
    return None, None


def generate_weak_labels(rewards):
    """生成弱标签"""
    # 使用与训练器相同的规则
    reward_clipped = np.clip(rewards, -5.0, 5.0)
    weak_labels = (reward_clipped + 5.0) / 10.0  # 映射到[0,1]
    return weak_labels


def test_segmentation_model():
    """测试分割模型"""
    print("🧠 测试基础分割模型...")
    
    # 加载数据
    images, rewards = load_sample_data()
    if images is None:
        print("❌ 无法加载测试数据")
        return False
    
    print(f"   加载了 {len(images)} 张图像")
    print(f"   奖励范围: [{rewards.min():.3f}, {rewards.max():.3f}]")
    
    # 生成弱标签
    weak_labels = generate_weak_labels(rewards)
    print(f"   弱标签范围: [{weak_labels.min():.3f}, {weak_labels.max():.3f}]")
    
    # 创建分割模型
    model = TaskAwarePassabilitySegNet(
        feature_channels=32,
        downsample_ratio=8,
        use_attention=True,
        use_contrastive=False,  # 简化测试
        use_temporal=False,
        name='test_seg'
    )
    
    # 初始化模型
    key = jax.random.PRNGKey(42)
    test_input = jnp.array(images[:1])  # 取第一张图像
    
    def init_fn(x):
        return model(x, task_context=None, prev_features=None)
    
    pure_init = nj.pure(init_fn)
    outputs, params = pure_init({}, key, test_input)
    
    print("✅ 模型初始化成功")
    print(f"   输出键: {list(outputs.keys())}")
    for k, v in outputs.items():
        if isinstance(v, jnp.ndarray):
            print(f"   {k}: {v.shape}")
    
    # 测试推理
    print("\n🔍 测试推理...")
    
    # 对所有图像进行推理
    batch_images = jnp.array(images)
    
    def forward_fn(params, x):
        return nj.pure(lambda x: model(x, task_context=None, prev_features=None))(params, key, x)
    
    all_outputs, _ = forward_fn(params, batch_images)
    
    # 分析结果
    scores = all_outputs['score']  # (N,)
    prob_maps = all_outputs['prob']  # (N, H, W, 1)
    
    print(f"   推理评分范围: [{scores.min():.3f}, {scores.max():.3f}]")
    print(f"   概率图范围: [{prob_maps.min():.3f}, {prob_maps.max():.3f}]")
    
    # 计算与弱标签的相关性
    correlation = np.corrcoef(np.array(scores), weak_labels)[0, 1]
    print(f"   评分与弱标签相关性: {correlation:.3f}")
    
    # 可视化结果
    visualize_results(images, rewards, weak_labels, scores, prob_maps)
    
    return True


def visualize_results(images, rewards, weak_labels, scores, prob_maps):
    """可视化结果"""
    print("\n🎨 生成可视化结果...")
    
    n_samples = min(6, len(images))
    fig, axes = plt.subplots(3, n_samples, figsize=(3*n_samples, 9))
    
    if n_samples == 1:
        axes = axes.reshape(-1, 1)
    
    for i in range(n_samples):
        # 原始图像
        axes[0, i].imshow(images[i])
        axes[0, i].set_title(f'Image {i+1}\nReward: {rewards[i]:.3f}')
        axes[0, i].axis('off')
        
        # 概率图
        prob_map = prob_maps[i, :, :, 0]
        im = axes[1, i].imshow(prob_map, cmap='RdYlGn', vmin=0, vmax=1)
        axes[1, i].set_title(f'Passability\nScore: {scores[i]:.3f}')
        axes[1, i].axis('off')
        
        # 弱标签对比
        axes[2, i].bar(['Weak Label', 'Model Score'], 
                      [weak_labels[i], scores[i]], 
                      color=['blue', 'orange'], alpha=0.7)
        axes[2, i].set_title('Label vs Score')
        axes[2, i].set_ylim(0, 1)
    
    plt.tight_layout()
    plt.savefig('simple_step1_test_results.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 相关性分析图
    plt.figure(figsize=(10, 6))
    
    plt.subplot(1, 2, 1)
    plt.scatter(rewards, weak_labels, alpha=0.7, color='blue', label='Reward → Weak Label')
    plt.xlabel('Reward')
    plt.ylabel('Weak Label')
    plt.title('Reward to Weak Label Mapping')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.subplot(1, 2, 2)
    plt.scatter(weak_labels, scores, alpha=0.7, color='orange', label='Weak Label vs Model Score')
    plt.plot([0, 1], [0, 1], 'r--', alpha=0.5, label='Perfect Correlation')
    plt.xlabel('Weak Label')
    plt.ylabel('Model Score')
    plt.title('Weak Label vs Model Score')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    correlation = np.corrcoef(weak_labels, np.array(scores))[0, 1]
    plt.suptitle(f'Step 1 Analysis (Correlation: {correlation:.3f})', fontsize=14)
    
    plt.tight_layout()
    plt.savefig('simple_step1_correlation_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 可视化结果已保存")


def main():
    """主函数"""
    print("🎯 简化第一步测试：弱标签生成 + 基础分割网络")
    print("=" * 60)
    
    success = test_segmentation_model()
    
    if success:
        print("\n✅ 第一步测试成功！")
        print("📋 总结:")
        print("   1. ✅ 数据加载正常")
        print("   2. ✅ 弱标签生成有效")
        print("   3. ✅ 分割模型可以运行")
        print("   4. ✅ 模型输出格式正确")
        print("\n📈 结果文件:")
        print("   - simple_step1_test_results.png")
        print("   - simple_step1_correlation_analysis.png")
    else:
        print("\n❌ 第一步测试失败")


if __name__ == "__main__":
    main()
