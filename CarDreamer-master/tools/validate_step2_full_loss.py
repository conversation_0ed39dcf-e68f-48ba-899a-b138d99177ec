"""
验证第二步：使用完整多层次监督损失函数的分割网络训练
对比简化MSE vs 完整多层次监督的效果差异
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import jax
import jax.numpy as jnp
from pathlib import Path
import cv2
import pickle

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from models.passability_seg_sota import TaskAwarePassabilitySegNet
from dreamerv3 import ninjax as nj


class FullLossValidator:
    """使用完整损失函数的验证器"""
    
    def __init__(self, data_dir="data/episodes_diverse"):
        self.data_dir = Path(data_dir)
        self.model = None
        self.params = None
        
    def load_sample_data(self, max_samples=50):
        """加载样本数据"""
        print("🔍 加载训练数据...")
        
        all_images = []
        all_rewards = []
        
        for data_type_dir in self.data_dir.iterdir():
            if not data_type_dir.is_dir():
                continue
                
            print(f"  📁 处理: {data_type_dir.name}")
            
            for ep_dir in data_type_dir.iterdir():
                if not ep_dir.is_dir() or not ep_dir.name.startswith('ep_'):
                    continue
                    
                meta_file = ep_dir / 'meta.npz'
                rgb_dir = ep_dir / 'rgb'
                
                if meta_file.exists() and rgb_dir.exists():
                    try:
                        meta_data = np.load(meta_file)
                        rewards = meta_data['rewards']
                        
                        rgb_files = []
                        for ext in ['*.png', '*.jpg', '*.jpeg']:
                            rgb_files.extend(list(rgb_dir.glob(ext)))
                        rgb_files = sorted(rgb_files)[:20]
                        
                        for i, rgb_file in enumerate(rgb_files):
                            if len(all_images) >= max_samples:
                                break
                                
                            img = cv2.imread(str(rgb_file))
                            if img is not None:
                                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                                img = cv2.resize(img, (128, 128))
                                
                                all_images.append(img)
                                reward_idx = min(i, len(rewards)-1)
                                all_rewards.append(rewards[reward_idx])
                        
                        if len(all_images) >= max_samples:
                            break
                            
                    except Exception as e:
                        continue
            
            if len(all_images) >= max_samples:
                break
        
        if len(all_images) == 0:
            return None, None
        
        images = np.array(all_images).astype(np.float32) / 255.0
        rewards = np.array(all_rewards).astype(np.float32)
        
        print(f"✅ 加载了 {len(images)} 个样本")
        print(f"   奖励范围: [{rewards.min():.3f}, {rewards.max():.3f}]")
        
        return images, rewards
    
    def create_model(self):
        """创建分割模型"""
        print("🧠 创建分割模型...")
        
        self.model = TaskAwarePassabilitySegNet(
            feature_channels=32,
            downsample_ratio=8,
            use_attention=True,
            use_contrastive=False,
            use_temporal=False,
            name='full_loss_seg'
        )
        
        print("✅ 模型创建成功")
    
    def initialize_model(self, sample_image):
        """初始化模型参数"""
        print("🔧 初始化模型参数...")
        
        key = jax.random.PRNGKey(42)
        test_input = jnp.expand_dims(jnp.array(sample_image), axis=0)
        
        def init_fn(x):
            return self.model(x, task_context=None, prev_features=None)
        
        pure_init = nj.pure(init_fn)
        outputs, params = pure_init({}, key, test_input)
        
        self.params = params
        print("✅ 模型初始化完成")
        return outputs
    
    def compute_full_passability_loss(self, outputs, rewards):
        """完整的可通行性损失函数（复制自TaskAwarePassabilityTrainer）"""
        prob_maps = outputs['prob']  # (B, H, W, 1)
        scores = outputs['score']    # (B,)

        # 奖励归一化
        reward_clipped = jnp.clip(rewards, -5.0, 5.0)
        reward_normalized = (reward_clipped + 5.0) / 10.0

        # 1. 基础标签（全局评分）
        score_loss = jnp.mean((scores - reward_normalized) ** 2)

        # 2. 空间标签（像素概率）
        avg_prob = jnp.mean(prob_maps, axis=(1, 2, 3))
        prob_loss = jnp.mean((avg_prob - reward_normalized) ** 2)

        # 3. 分层标签（奖励等级）
        high_reward_mask = rewards > 2.5
        low_reward_mask = rewards < 1.5

        high_reward_loss = jnp.where(
            jnp.sum(high_reward_mask) > 0,
            jnp.mean(jnp.where(high_reward_mask, (scores - 0.8) ** 2, 0.0)),
            0.0
        )

        low_reward_loss = jnp.where(
            jnp.sum(low_reward_mask) > 0,
            jnp.mean(jnp.where(low_reward_mask, (scores - 0.2) ** 2, 0.0)),
            0.0
        )

        total_passability_loss = score_loss + prob_loss + 0.5 * (high_reward_loss + low_reward_loss)
        
        return total_passability_loss, {
            'score_loss': score_loss,
            'prob_loss': prob_loss, 
            'high_reward_loss': high_reward_loss,
            'low_reward_loss': low_reward_loss
        }
    
    def training_comparison(self, images, rewards, num_epochs=20):
        """对比简化MSE vs 完整多层次监督"""
        print(f"🚀 开始对比训练 ({num_epochs} epochs)...")
        
        # 生成弱标签
        reward_clipped = np.clip(rewards, -5.0, 5.0)
        weak_labels = (reward_clipped + 5.0) / 10.0
        
        # 创建两个优化器
        import optax
        optimizer1 = optax.adam(1e-4)  # 简化MSE
        optimizer2 = optax.adam(1e-4)  # 完整损失
        
        # 初始化两套参数（相同的初始化）
        params1 = jax.tree_map(lambda x: x.copy(), self.params)
        params2 = jax.tree_map(lambda x: x.copy(), self.params)
        
        opt_state1 = optimizer1.init(params1)
        opt_state2 = optimizer2.init(params2)
        
        # 定义简化MSE损失函数
        def simple_loss_fn(params, batch_images, batch_labels):
            def forward_fn(x):
                return self.model(x, task_context=None, prev_features=None)
            
            pure_forward = nj.pure(forward_fn)
            outputs, _ = pure_forward(params, jax.random.PRNGKey(0), batch_images)
            
            scores = outputs['score']
            loss = jnp.mean((scores - batch_labels) ** 2)
            
            return loss, {'simple_loss': loss, 'scores': scores}
        
        # 定义完整损失函数
        def full_loss_fn(params, batch_images, batch_rewards):
            def forward_fn(x):
                return self.model(x, task_context=None, prev_features=None)
            
            pure_forward = nj.pure(forward_fn)
            outputs, _ = pure_forward(params, jax.random.PRNGKey(0), batch_images)
            
            full_loss, loss_components = self.compute_full_passability_loss(outputs, batch_rewards)
            
            return full_loss, {
                'full_loss': full_loss,
                'scores': outputs['score'],
                **loss_components
            }
        
        # JIT编译
        grad_fn1 = jax.jit(jax.value_and_grad(simple_loss_fn, has_aux=True))
        grad_fn2 = jax.jit(jax.value_and_grad(full_loss_fn, has_aux=True))
        
        # 训练历史
        simple_losses = []
        full_losses = []
        simple_correlations = []
        full_correlations = []
        
        batch_size = 4
        
        for epoch in range(num_epochs):
            epoch_simple_losses = []
            epoch_full_losses = []
            
            # 随机打乱数据
            indices = np.random.permutation(len(images))
            
            for i in range(0, len(images), batch_size):
                batch_indices = indices[i:i+batch_size]
                if len(batch_indices) < batch_size:
                    continue
                
                batch_images = jnp.array(images[batch_indices])
                batch_labels = jnp.array(weak_labels[batch_indices])
                batch_rewards = jnp.array(rewards[batch_indices])
                
                try:
                    # 训练简化MSE模型
                    (loss1, aux1), grads1 = grad_fn1(params1, batch_images, batch_labels)
                    updates1, opt_state1 = optimizer1.update(grads1, opt_state1)
                    params1 = optax.apply_updates(params1, updates1)
                    epoch_simple_losses.append(float(loss1))
                    
                    # 训练完整损失模型
                    (loss2, aux2), grads2 = grad_fn2(params2, batch_images, batch_rewards)
                    updates2, opt_state2 = optimizer2.update(grads2, opt_state2)
                    params2 = optax.apply_updates(params2, updates2)
                    epoch_full_losses.append(float(loss2))
                    
                except Exception as e:
                    continue
            
            if epoch_simple_losses and epoch_full_losses:
                simple_losses.append(np.mean(epoch_simple_losses))
                full_losses.append(np.mean(epoch_full_losses))
                
                # 计算相关性
                if (epoch + 1) % 5 == 0:
                    # 简化模型相关性
                    simple_scores = self.evaluate_model(params1, images)
                    simple_corr = np.corrcoef(simple_scores, weak_labels)[0, 1] if len(simple_scores) > 1 else 0.0
                    simple_correlations.append(simple_corr)
                    
                    # 完整模型相关性
                    full_scores = self.evaluate_model(params2, images)
                    full_corr = np.corrcoef(full_scores, weak_labels)[0, 1] if len(full_scores) > 1 else 0.0
                    full_correlations.append(full_corr)
                    
                    print(f"    Epoch {epoch+1:3d}: Simple={simple_losses[-1]:.6f} (r={simple_corr:.3f}), Full={full_losses[-1]:.6f} (r={full_corr:.3f})")
        
        return {
            'simple_losses': simple_losses,
            'full_losses': full_losses,
            'simple_correlations': simple_correlations,
            'full_correlations': full_correlations,
            'simple_params': params1,
            'full_params': params2
        }
    
    def evaluate_model(self, params, images):
        """评估模型并返回评分"""
        batch_size = 8
        all_scores = []
        
        for i in range(0, len(images), batch_size):
            batch_images = images[i:i+batch_size]
            batch_images_jax = jnp.array(batch_images)
            
            try:
                def forward_fn(x):
                    return self.model(x, task_context=None, prev_features=None)
                
                pure_forward = nj.pure(forward_fn)
                outputs, _ = pure_forward(params, jax.random.PRNGKey(0), batch_images_jax)
                
                all_scores.extend(outputs['score'])
                
            except Exception as e:
                continue
        
        return np.array(all_scores)
    
    def visualize_comparison(self, results, images, rewards):
        """可视化对比结果"""
        print("🎨 生成对比可视化...")
        
        plt.figure(figsize=(15, 10))
        
        # 损失对比
        plt.subplot(2, 3, 1)
        plt.plot(results['simple_losses'], 'b-', label='Simple MSE', linewidth=2)
        plt.plot(results['full_losses'], 'r-', label='Full Multi-level', linewidth=2)
        plt.title('Training Loss Comparison')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 相关性对比
        plt.subplot(2, 3, 2)
        if results['simple_correlations'] and results['full_correlations']:
            epochs = np.arange(5, len(results['simple_losses'])+1, 5)[:len(results['simple_correlations'])]
            plt.plot(epochs, results['simple_correlations'], 'b-o', label='Simple MSE', linewidth=2)
            plt.plot(epochs, results['full_correlations'], 'r-o', label='Full Multi-level', linewidth=2)
            plt.title('Correlation Comparison')
            plt.xlabel('Epoch')
            plt.ylabel('Correlation')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        # 最终性能对比
        plt.subplot(2, 3, 3)
        if results['simple_correlations'] and results['full_correlations']:
            final_simple = results['simple_correlations'][-1]
            final_full = results['full_correlations'][-1]
            
            plt.bar(['Simple MSE', 'Full Multi-level'], [final_simple, final_full], 
                   color=['blue', 'red'], alpha=0.7)
            plt.title('Final Correlation Comparison')
            plt.ylabel('Correlation')
            plt.ylim(0, 1)
            
            # 添加数值标签
            plt.text(0, final_simple + 0.02, f'{final_simple:.3f}', ha='center')
            plt.text(1, final_full + 0.02, f'{final_full:.3f}', ha='center')
        
        # 损失改善对比
        plt.subplot(2, 3, 4)
        simple_improvement = (results['simple_losses'][0] - results['simple_losses'][-1]) / results['simple_losses'][0] * 100
        full_improvement = (results['full_losses'][0] - results['full_losses'][-1]) / results['full_losses'][0] * 100
        
        plt.bar(['Simple MSE', 'Full Multi-level'], [simple_improvement, full_improvement], 
               color=['blue', 'red'], alpha=0.7)
        plt.title('Loss Improvement (%)')
        plt.ylabel('Improvement (%)')
        
        # 添加数值标签
        plt.text(0, simple_improvement + 1, f'{simple_improvement:.1f}%', ha='center')
        plt.text(1, full_improvement + 1, f'{full_improvement:.1f}%', ha='center')
        
        # 训练稳定性对比
        plt.subplot(2, 3, 5)
        simple_std = np.std(results['simple_losses'][-10:]) if len(results['simple_losses']) >= 10 else 0
        full_std = np.std(results['full_losses'][-10:]) if len(results['full_losses']) >= 10 else 0
        
        plt.bar(['Simple MSE', 'Full Multi-level'], [simple_std, full_std], 
               color=['blue', 'red'], alpha=0.7)
        plt.title('Training Stability (Last 10 epochs std)')
        plt.ylabel('Loss Std')
        
        # 总结
        plt.subplot(2, 3, 6)
        plt.text(0.1, 0.8, 'Comparison Summary:', fontsize=14, fontweight='bold')
        
        if results['simple_correlations'] and results['full_correlations']:
            final_simple = results['simple_correlations'][-1]
            final_full = results['full_correlations'][-1]
            improvement = ((final_full - final_simple) / final_simple * 100) if final_simple > 0 else 0
            
            plt.text(0.1, 0.6, f'Simple MSE: {final_simple:.3f}', fontsize=12)
            plt.text(0.1, 0.5, f'Full Multi-level: {final_full:.3f}', fontsize=12)
            plt.text(0.1, 0.4, f'Improvement: {improvement:.1f}%', fontsize=12, 
                    color='green' if improvement > 0 else 'red')
            
            if improvement > 20:
                plt.text(0.1, 0.2, '🎉 Significant Improvement!', fontsize=12, color='green')
            elif improvement > 5:
                plt.text(0.1, 0.2, '✅ Moderate Improvement', fontsize=12, color='orange')
            else:
                plt.text(0.1, 0.2, '⚠️  Minimal Difference', fontsize=12, color='red')
        
        plt.axis('off')
        
        plt.tight_layout()
        plt.savefig('step2_loss_comparison.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✅ 对比可视化完成")
    
    def run_validation(self):
        """运行完整验证"""
        print("🎯 开始验证：简化MSE vs 完整多层次监督")
        print("=" * 60)
        
        # 1. 加载数据
        images, rewards = self.load_sample_data(max_samples=40)
        if images is None:
            return
        
        # 2. 创建和初始化模型
        self.create_model()
        self.initialize_model(images[0])
        
        # 3. 对比训练
        results = self.training_comparison(images, rewards, num_epochs=15)
        
        # 4. 可视化对比
        self.visualize_comparison(results, images, rewards)
        
        # 5. 生成报告
        print("\n📋 损失函数对比报告")
        print("=" * 40)
        
        if results['simple_correlations'] and results['full_correlations']:
            final_simple = results['simple_correlations'][-1]
            final_full = results['full_correlations'][-1]
            improvement = ((final_full - final_simple) / final_simple * 100) if final_simple > 0 else 0
            
            print(f"📊 最终相关性对比:")
            print(f"   简化MSE: {final_simple:.3f}")
            print(f"   完整多层次: {final_full:.3f}")
            print(f"   改善程度: {improvement:.1f}%")
            
            if improvement > 20:
                print("🎉 完整多层次监督显著优于简化MSE！")
            elif improvement > 5:
                print("✅ 完整多层次监督有明显改善")
            else:
                print("⚠️  两种方法效果相近，可能需要更多数据或训练")
        
        # 保存结果
        with open('step2_loss_comparison_report.pkl', 'wb') as f:
            pickle.dump(results, f)
        
        print(f"\n💾 对比报告已保存: step2_loss_comparison_report.pkl")


def main():
    """主函数"""
    validator = FullLossValidator()
    validator.run_validation()


if __name__ == "__main__":
    main()
