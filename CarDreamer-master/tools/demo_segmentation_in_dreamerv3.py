"""
演示分割网络在DreamerV3中的应用
展示任务感知分割网络如何与DreamerV3集成并产生语义分割图
"""
import os
import sys
import time
import numpy as np
import matplotlib.pyplot as plt
import cv2
import pickle
import jax
import jax.numpy as jnp
from jax import random
from typing import Dict, List, Tuple, Optional

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'dreamerv3'))

# 导入模型和组件
from models.passability_seg_sota import TaskAwarePassabilitySegNet
from dreamerv3 import ninjax as nj
from dreamerv3.enhanced_nets import EnhancedMultiEncoder

# 尝试导入CARLA
try:
    import carla
    from tools.carla_online_env import CARLAEnvironment
    CARLA_AVAILABLE = True
except ImportError:
    CARLA_AVAILABLE = False
    print("CARLA not available, using simulation mode")


class SegmentationDemo:
    """分割网络演示类"""
    
    def __init__(self, model_path=None):
        self.model_path = model_path or 'checkpoints/h_stage/final_h_stage_model.pkl'
        self.seg_model = None
        self.seg_params = None
        self.carla_env = None
        
        # 初始化模型
        self._initialize_model()
        
        # 如果CARLA可用，初始化环境
        if CARLA_AVAILABLE:
            self._initialize_carla()
    
    def _initialize_model(self):
        """初始化分割模型"""
        print("🧠 Initializing segmentation model...")
        
        # 创建分割网络
        self.seg_model = TaskAwarePassabilitySegNet(
            feature_channels=64,
            downsample_ratio=8,
            use_attention=True,
            use_contrastive=True,
            use_temporal=True,
            name='task_aware_seg'
        )
        
        # 尝试加载预训练参数
        try:
            with open(self.model_path, 'rb') as f:
                model_data = pickle.load(f)
                if 'seg_params' in model_data:
                    self.seg_params = model_data['seg_params']
                    print("✅ Loaded pretrained segmentation parameters")
                else:
                    print("⚠️  No segmentation parameters found, using random initialization")
        except Exception as e:
            print(f"⚠️  Could not load model: {e}, using random initialization")
    
    def _initialize_carla(self):
        """初始化CARLA环境"""
        print("🚗 Initializing CARLA environment...")
        try:
            self.carla_env = CARLAEnvironment()
            if self.carla_env.connect():
                if self.carla_env.spawn_vehicle() and self.carla_env.setup_sensors():
                    print("✅ CARLA environment ready")
                else:
                    print("⚠️  CARLA vehicle/sensor setup failed")
                    self.carla_env = None
            else:
                print("⚠️  CARLA connection failed")
                self.carla_env = None
        except Exception as e:
            print(f"⚠️  CARLA initialization error: {e}")
            self.carla_env = None
    
    def create_test_images(self) -> List[Tuple[str, np.ndarray, float]]:
        """创建测试图像"""
        test_images = []
        
        # 1. 清晰道路场景（高可通行性）
        img1 = np.zeros((128, 128, 3), dtype=np.float32)
        # 道路区域
        img1[50:78, :, :] = [0.5, 0.5, 0.5]  # 灰色道路
        # 车道线
        img1[49:50, :, :] = [1.0, 1.0, 1.0]  # 白色车道线
        img1[78:79, :, :] = [1.0, 1.0, 1.0]
        img1[63:65, :, :] = [1.0, 1.0, 1.0]  # 中央分隔线
        test_images.append(("Clear Road (High Passability)", img1, 1.5))
        
        # 2. 有障碍物的道路（中等可通行性）
        img2 = np.zeros((128, 128, 3), dtype=np.float32)
        img2[45:83, :, :] = [0.4, 0.4, 0.4]  # 道路
        # 障碍物
        img2[55:65, 30:40, :] = [0.8, 0.4, 0.2]  # 橙色障碍物
        img2[60:70, 90:100, :] = [0.6, 0.3, 0.3]  # 红褐色障碍物
        # 部分车道线
        img2[44:45, :30, :] = [1.0, 1.0, 1.0]
        img2[44:45, 50:90, :] = [1.0, 1.0, 1.0]
        test_images.append(("Road with Obstacles (Medium Passability)", img2, 0.5))
        
        # 3. 复杂场景（低可通行性）
        img3 = np.zeros((128, 128, 3), dtype=np.float32)
        # 不规则道路
        for i in range(128):
            road_width = int(25 + 10 * np.sin(i * 0.1))
            road_center = int(64 + 15 * np.cos(i * 0.08))
            start = max(0, road_center - road_width // 2)
            end = min(128, road_center + road_width // 2)
            img3[start:end, i, :] = [0.3, 0.3, 0.3]
        
        # 多个障碍物
        obstacles = [(20, 30, 10, 15), (70, 50, 15, 20), (40, 80, 12, 18)]
        for x, y, w, h in obstacles:
            img3[x:x+w, y:y+h, :] = [1.0, 0.0, 0.0]  # 红色障碍物
        test_images.append(("Complex Scene (Low Passability)", img3, -0.3))
        
        return test_images
    
    def run_segmentation_inference(self, image: np.ndarray, reward: float = 0.0) -> Dict:
        """运行分割推理"""
        # 准备输入
        batch_image = jnp.expand_dims(jnp.array(image), axis=0)  # (1, H, W, 3)
        batch_reward = jnp.array([reward])  # (1,)
        
        # 创建任务上下文（基于奖励）
        task_context = jnp.array([[reward, reward**2, np.abs(reward)]])  # (1, 3)
        
        # 如果有预训练参数，使用它们
        if self.seg_params is not None:
            # 使用预训练参数进行推理
            def forward_fn(params, x, task_ctx):
                return nj.pure(self.seg_model.__call__)(params, jax.random.PRNGKey(0), x, 
                                                       task_context=task_ctx, prev_features=None)
            
            outputs, _ = forward_fn(self.seg_params, batch_image, task_context)
        else:
            # 使用随机初始化参数
            key = jax.random.PRNGKey(42)
            dummy_params = {}  # 简化的参数初始化
            
            # 直接调用模型（会自动初始化参数）
            def init_and_forward(x, task_ctx):
                return self.seg_model(x, task_context=task_ctx, prev_features=None)
            
            pure_forward = nj.pure(init_and_forward)
            outputs, new_params = pure_forward({}, key, batch_image, task_context)
            self.seg_params = new_params  # 保存参数供后续使用
        
        return outputs
    
    def visualize_segmentation_results(self, image: np.ndarray, outputs: Dict, title: str):
        """可视化分割结果"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f'Task-aware Segmentation Results: {title}', fontsize=16)
        
        # 原始图像
        axes[0, 0].imshow(image)
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        # 可通行性概率图
        prob_map = outputs['prob'][0, :, :, 0]  # (H, W)
        im1 = axes[0, 1].imshow(prob_map, cmap='RdYlGn', vmin=0, vmax=1)
        axes[0, 1].set_title(f'Passability Probability\n(Score: {outputs["score"][0]:.3f})')
        axes[0, 1].axis('off')
        plt.colorbar(im1, ax=axes[0, 1], fraction=0.046, pad=0.04)
        
        # 特征图可视化（取前3个通道）
        feat_map = outputs['feat'][0, :, :, :3]  # (H, W, 3)
        feat_map = (feat_map - feat_map.min()) / (feat_map.max() - feat_map.min() + 1e-8)
        axes[0, 2].imshow(feat_map)
        axes[0, 2].set_title('Feature Map (RGB)')
        axes[0, 2].axis('off')
        
        # 任务感知评分
        task_score = outputs.get('task_score', outputs['score'])[0]
        axes[1, 0].bar(['Passability', 'Task-aware'], 
                      [outputs['score'][0], task_score],
                      color=['blue', 'orange'])
        axes[1, 0].set_title('Scores Comparison')
        axes[1, 0].set_ylabel('Score')
        
        # 注意力权重（如果有）
        if 'attention_weights' in outputs:
            att_weights = outputs['attention_weights'][0, :, :, 0]
            im2 = axes[1, 1].imshow(att_weights, cmap='hot')
            axes[1, 1].set_title('Attention Weights')
            axes[1, 1].axis('off')
            plt.colorbar(im2, ax=axes[1, 1], fraction=0.046, pad=0.04)
        else:
            axes[1, 1].text(0.5, 0.5, 'No Attention\nWeights Available', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].axis('off')
        
        # 全局特征可视化
        if 'global_feat' in outputs:
            global_feat = outputs['global_feat'][0]  # (feature_dim,)
            axes[1, 2].plot(global_feat[:min(64, len(global_feat))])
            axes[1, 2].set_title('Global Features (first 64 dims)')
            axes[1, 2].set_xlabel('Feature Index')
            axes[1, 2].set_ylabel('Feature Value')
        else:
            axes[1, 2].text(0.5, 0.5, 'No Global\nFeatures Available', 
                           ha='center', va='center', transform=axes[1, 2].transAxes)
            axes[1, 2].axis('off')
        
        plt.tight_layout()
        return fig

    def run_carla_demo(self, duration=60):
        """运行CARLA实时演示"""
        if not self.carla_env:
            print("❌ CARLA environment not available")
            return

        print(f"🚗 Starting CARLA demo for {duration} seconds...")
        start_time = time.time()
        frame_count = 0

        # 创建实时显示窗口
        cv2.namedWindow('CARLA Segmentation Demo', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('CARLA Segmentation Demo', 1200, 800)

        try:
            while time.time() - start_time < duration:
                # 获取CARLA观察
                observation = self.carla_env.get_observation()
                if observation is None:
                    continue

                # 获取RGB图像
                rgb_image = observation.get('camera', np.zeros((128, 128, 3)))
                if rgb_image.shape != (128, 128, 3):
                    rgb_image = cv2.resize(rgb_image, (128, 128))

                # 归一化到[0,1]
                rgb_image = rgb_image.astype(np.float32) / 255.0

                # 计算当前奖励（基于车辆状态）
                vehicle_state = observation.get('vehicle_state', {})
                velocity = vehicle_state.get('velocity', {'x': 0, 'y': 0})
                speed = np.sqrt(velocity['x']**2 + velocity['y']**2)
                reward = min(speed / 10.0, 1.0)  # 简单的速度奖励

                # 运行分割推理
                start_inference = time.time()
                outputs = self.run_segmentation_inference(rgb_image, reward)
                inference_time = time.time() - start_inference

                # 创建显示图像
                display_image = self.create_display_image(rgb_image, outputs, inference_time, reward)

                # 显示结果
                cv2.imshow('CARLA Segmentation Demo', display_image)

                # 检查退出条件
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break

                frame_count += 1

                # 简单的车辆控制（直行）
                control = {'throttle': 0.3, 'steer': 0.0, 'brake': 0.0}
                self.carla_env.apply_control(control)

        except KeyboardInterrupt:
            print("Demo interrupted by user")
        finally:
            cv2.destroyAllWindows()
            print(f"Demo completed. Processed {frame_count} frames")

    def create_display_image(self, rgb_image, outputs, inference_time, reward):
        """创建用于显示的图像"""
        # 转换为显示格式
        rgb_display = (rgb_image * 255).astype(np.uint8)
        rgb_display = cv2.resize(rgb_display, (400, 400))

        # 获取分割结果
        prob_map = outputs['prob'][0, :, :, 0]
        prob_display = (prob_map * 255).astype(np.uint8)
        prob_display = cv2.resize(prob_display, (400, 400))
        prob_display = cv2.applyColorMap(prob_display, cv2.COLORMAP_RdYlGn)

        # 创建组合图像
        combined = np.zeros((400, 800, 3), dtype=np.uint8)
        combined[:, :400] = cv2.cvtColor(rgb_display, cv2.COLOR_RGB2BGR)
        combined[:, 400:] = prob_display

        # 添加文本信息
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(combined, 'Original Image', (10, 30), font, 0.7, (255, 255, 255), 2)
        cv2.putText(combined, 'Passability Map', (410, 30), font, 0.7, (255, 255, 255), 2)

        # 添加性能信息
        score = outputs['score'][0]
        cv2.putText(combined, f'Score: {score:.3f}', (10, 370), font, 0.6, (0, 255, 0), 2)
        cv2.putText(combined, f'Reward: {reward:.3f}', (10, 390), font, 0.6, (0, 255, 0), 2)
        cv2.putText(combined, f'Inference: {inference_time*1000:.1f}ms', (410, 370), font, 0.6, (0, 255, 0), 2)

        return combined

    def run_test_demo(self):
        """运行测试图像演示"""
        print("🎨 Running test image demo...")

        # 创建测试图像
        test_images = self.create_test_images()

        # 为每个测试图像运行推理并可视化
        for i, (title, image, reward) in enumerate(test_images):
            print(f"Processing: {title}")

            # 运行推理
            outputs = self.run_segmentation_inference(image, reward)

            # 可视化结果
            fig = self.visualize_segmentation_results(image, outputs, title)

            # 保存结果
            output_path = f'segmentation_demo_{i+1}_{title.replace(" ", "_").replace("(", "").replace(")", "")}.png'
            fig.savefig(output_path, dpi=150, bbox_inches='tight')
            print(f"Saved: {output_path}")

            # 显示结果
            plt.show()

            # 等待用户输入继续
            input("Press Enter to continue to next image...")
            plt.close(fig)


def main():
    """主函数"""
    print("🎯 Task-aware Segmentation in DreamerV3 Demo")
    print("=" * 50)

    # 创建演示实例
    demo = SegmentationDemo()

    # 选择演示模式
    print("\nSelect demo mode:")
    print("1. Test images demo (recommended)")
    print("2. CARLA real-time demo (requires CARLA)")
    print("3. Both")

    choice = input("Enter your choice (1/2/3): ").strip()

    if choice in ['1', '3']:
        demo.run_test_demo()

    if choice in ['2', '3']:
        if CARLA_AVAILABLE and demo.carla_env:
            duration = int(input("Enter demo duration in seconds (default 60): ") or "60")
            demo.run_carla_demo(duration)
        else:
            print("❌ CARLA demo not available")

    print("\n✅ Demo completed!")


if __name__ == "__main__":
    main()
