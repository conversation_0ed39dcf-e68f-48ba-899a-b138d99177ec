"""
验证第二步：分割网络训练
验证TaskAwarePassabilitySegNet能否从弱标签中学习有效的可通行性分割
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import jax
import jax.numpy as jnp
from pathlib import Path
import cv2
import pickle
import time
from typing import Dict, List, Tuple

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from models.passability_seg_sota import TaskAwarePassabilitySegNet
from tools.task_aware_passability_trainer import TaskAwarePassabilityTrainer
from dreamerv3 import ninjax as nj


class Step2Validator:
    """第二步验证器：分割网络训练"""
    
    def __init__(self, data_dir="data/episodes_diverse"):
        self.data_dir = Path(data_dir)
        self.trainer = None
        self.training_history = []
        
    def load_training_data(self, max_samples=200):
        """加载训练数据"""
        print("🔍 加载训练数据...")
        
        all_images = []
        all_rewards = []
        all_data_types = []
        
        # 遍历所有数据目录
        for data_type_dir in self.data_dir.iterdir():
            if not data_type_dir.is_dir():
                continue
                
            data_type = data_type_dir.name
            print(f"  📁 处理数据类型: {data_type}")
            
            for ep_dir in data_type_dir.iterdir():
                if not ep_dir.is_dir() or not ep_dir.name.startswith('ep_'):
                    continue
                    
                meta_file = ep_dir / 'meta.npz'
                rgb_dir = ep_dir / 'rgb'
                
                if meta_file.exists() and rgb_dir.exists():
                    try:
                        # 加载元数据
                        meta_data = np.load(meta_file)
                        rewards = meta_data['rewards']
                        
                        # 加载图像
                        rgb_files = []
                        for ext in ['*.png', '*.jpg', '*.jpeg']:
                            rgb_files.extend(list(rgb_dir.glob(ext)))
                        rgb_files = sorted(rgb_files)
                        
                        # 每个episode取一些样本
                        step_size = max(1, len(rgb_files) // 10)  # 每个episode最多取10张
                        selected_files = rgb_files[::step_size][:10]
                        
                        for i, rgb_file in enumerate(selected_files):
                            if len(all_images) >= max_samples:
                                break
                                
                            img = cv2.imread(str(rgb_file))
                            if img is not None:
                                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                                img = cv2.resize(img, (128, 128))
                                
                                all_images.append(img)
                                all_rewards.append(rewards[i * step_size] if i * step_size < len(rewards) else rewards[-1])
                                all_data_types.append(data_type)
                        
                        if len(all_images) >= max_samples:
                            break
                            
                    except Exception as e:
                        print(f"    ⚠️  加载失败 {ep_dir.name}: {e}")
                        continue
            
            if len(all_images) >= max_samples:
                break
        
        if len(all_images) == 0:
            print("❌ 没有找到有效的训练数据")
            return None, None, None
        
        # 转换为numpy数组
        images = np.array(all_images).astype(np.float32) / 255.0
        rewards = np.array(all_rewards).astype(np.float32)
        
        print(f"✅ 加载完成:")
        print(f"   总样本数: {len(images)}")
        print(f"   图像形状: {images.shape}")
        print(f"   奖励范围: [{rewards.min():.3f}, {rewards.max():.3f}]")
        
        return images, rewards, all_data_types
    
    def create_trainer(self):
        """创建训练器"""
        print("🧠 创建分割网络训练器...")
        
        self.trainer = TaskAwarePassabilityTrainer(
            feature_channels=64,
            downsample_ratio=8,
            learning_rate=1e-4,
            batch_size=8
        )
        
        # 初始化
        rng_key = jax.random.PRNGKey(42)
        self.trainer.initialize(rng_key)
        
        print("✅ 训练器创建成功")
    
    def add_training_data(self, images, rewards, data_types):
        """添加训练数据到训练器"""
        print("📊 添加训练数据...")
        
        # 生成弱标签
        reward_clipped = np.clip(rewards, -5.0, 5.0)
        weak_labels = (reward_clipped + 5.0) / 10.0
        
        # 添加数据到训练器的缓冲区
        for i in range(len(images)):
            data_point = {
                'image': images[i],
                'reward': rewards[i],
                'weak_label': weak_labels[i],
                'data_type': data_types[i]
            }
            self.trainer.add_data(data_point)
        
        print(f"✅ 添加了 {len(images)} 个训练样本")
        print(f"   弱标签范围: [{weak_labels.min():.3f}, {weak_labels.max():.3f}]")
    
    def train_network(self, num_epochs=50):
        """训练分割网络"""
        print(f"🚀 开始训练分割网络 ({num_epochs} epochs)...")
        
        best_loss = float('inf')
        patience = 10
        no_improve_count = 0
        
        for epoch in range(num_epochs):
            epoch_losses = []
            epoch_metrics = {}
            
            # 训练多个批次
            num_batches = max(1, len(self.trainer.data_buffer) // self.trainer.batch_size)
            
            for batch_idx in range(min(num_batches, 10)):  # 限制每个epoch的批次数
                try:
                    # 执行训练步骤
                    loss_info = self.trainer.train_step()
                    
                    if loss_info is not None:
                        total_loss = loss_info.get('total_loss', 0.0)
                        epoch_losses.append(float(total_loss))
                        
                        # 收集其他指标
                        for key, value in loss_info.items():
                            if key not in epoch_metrics:
                                epoch_metrics[key] = []
                            epoch_metrics[key].append(float(value))
                
                except Exception as e:
                    print(f"    ⚠️  批次 {batch_idx} 训练失败: {e}")
                    continue
            
            if not epoch_losses:
                print(f"    Epoch {epoch+1}: 没有有效的训练批次")
                continue
            
            # 计算平均指标
            avg_loss = np.mean(epoch_losses)
            avg_metrics = {k: np.mean(v) for k, v in epoch_metrics.items() if v}
            
            # 记录训练历史
            history_entry = {
                'epoch': epoch + 1,
                'loss': avg_loss,
                **avg_metrics
            }
            self.training_history.append(history_entry)
            
            # 打印进度
            if (epoch + 1) % 5 == 0 or epoch < 10:
                print(f"    Epoch {epoch+1:3d}: Loss = {avg_loss:.6f}")
                if 'passability_loss' in avg_metrics:
                    print(f"                Passability = {avg_metrics['passability_loss']:.6f}")
                if 'task_aware_loss' in avg_metrics:
                    print(f"                Task-aware = {avg_metrics['task_aware_loss']:.6f}")
            
            # 早停检查
            if avg_loss < best_loss:
                best_loss = avg_loss
                no_improve_count = 0
            else:
                no_improve_count += 1
                
            if no_improve_count >= patience:
                print(f"    早停：{patience} epochs无改善")
                break
        
        print(f"✅ 训练完成！最佳损失: {best_loss:.6f}")
        return best_loss
    
    def evaluate_network(self, test_images, test_rewards, test_data_types):
        """评估训练后的网络"""
        print("🔍 评估训练后的网络...")
        
        # 生成测试弱标签
        test_reward_clipped = np.clip(test_rewards, -5.0, 5.0)
        test_weak_labels = (test_reward_clipped + 5.0) / 10.0
        
        # 批量推理
        batch_size = 8
        all_outputs = []
        
        for i in range(0, len(test_images), batch_size):
            batch_images = test_images[i:i+batch_size]
            batch_images_jax = jnp.array(batch_images)
            
            try:
                # 使用训练器的模型进行推理
                def inference_fn(params, x):
                    return nj.pure(lambda x: self.trainer.model(x, task_context=None, prev_features=None))(
                        params, jax.random.PRNGKey(0), x
                    )
                
                outputs, _ = inference_fn(self.trainer.params, batch_images_jax)
                all_outputs.append(outputs)
                
            except Exception as e:
                print(f"    ⚠️  推理批次 {i//batch_size} 失败: {e}")
                continue
        
        if not all_outputs:
            print("❌ 推理失败")
            return None
        
        # 合并所有输出
        combined_outputs = {}
        for key in all_outputs[0].keys():
            combined_outputs[key] = jnp.concatenate([out[key] for out in all_outputs], axis=0)
        
        # 计算评估指标
        scores = np.array(combined_outputs['score'])
        prob_maps = np.array(combined_outputs['prob'])
        
        # 相关性分析
        correlation = np.corrcoef(scores, test_weak_labels)[0, 1]
        
        # 不同数据类型的性能
        type_performance = {}
        unique_types = list(set(test_data_types))
        
        for data_type in unique_types:
            type_mask = np.array([dt == data_type for dt in test_data_types])
            if np.sum(type_mask) > 0:
                type_scores = scores[type_mask]
                type_labels = test_weak_labels[type_mask]
                type_corr = np.corrcoef(type_scores, type_labels)[0, 1] if len(type_scores) > 1 else 0.0
                type_performance[data_type] = {
                    'correlation': type_corr,
                    'avg_score': np.mean(type_scores),
                    'avg_label': np.mean(type_labels),
                    'count': np.sum(type_mask)
                }
        
        evaluation_results = {
            'overall_correlation': correlation,
            'score_range': [float(scores.min()), float(scores.max())],
            'score_mean': float(scores.mean()),
            'score_std': float(scores.std()),
            'type_performance': type_performance
        }
        
        print(f"✅ 评估完成:")
        print(f"   整体相关性: {correlation:.3f}")
        print(f"   评分范围: [{scores.min():.3f}, {scores.max():.3f}]")
        print(f"   评分均值: {scores.mean():.3f} ± {scores.std():.3f}")
        
        return evaluation_results, combined_outputs
    
    def visualize_results(self, test_images, test_rewards, test_data_types, outputs, evaluation_results):
        """可视化训练结果"""
        print("🎨 生成可视化结果...")
        
        # 生成弱标签
        test_reward_clipped = np.clip(test_rewards, -5.0, 5.0)
        test_weak_labels = (test_reward_clipped + 5.0) / 10.0
        
        scores = np.array(outputs['score'])
        prob_maps = np.array(outputs['prob'])
        
        # 1. 训练曲线
        if self.training_history:
            plt.figure(figsize=(15, 10))
            
            # 损失曲线
            plt.subplot(2, 3, 1)
            epochs = [h['epoch'] for h in self.training_history]
            losses = [h['loss'] for h in self.training_history]
            plt.plot(epochs, losses, 'b-', linewidth=2)
            plt.title('训练损失曲线')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.grid(True, alpha=0.3)
            
            # 相关性变化（如果有记录）
            plt.subplot(2, 3, 2)
            if 'correlation' in self.training_history[0]:
                correlations = [h.get('correlation', 0) for h in self.training_history]
                plt.plot(epochs, correlations, 'g-', linewidth=2)
                plt.title('训练过程中的相关性')
                plt.xlabel('Epoch')
                plt.ylabel('Correlation')
                plt.grid(True, alpha=0.3)
            else:
                plt.text(0.5, 0.5, '无相关性记录', ha='center', va='center', transform=plt.gca().transAxes)
            
            # 预测vs真实标签散点图
            plt.subplot(2, 3, 3)
            plt.scatter(test_weak_labels, scores, alpha=0.6, s=20)
            plt.plot([0, 1], [0, 1], 'r--', alpha=0.8, label='Perfect Correlation')
            plt.xlabel('弱标签 (真实)')
            plt.ylabel('网络评分 (预测)')
            plt.title(f'预测准确性 (r={evaluation_results["overall_correlation"]:.3f})')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # 不同数据类型的性能
            plt.subplot(2, 3, 4)
            type_names = list(evaluation_results['type_performance'].keys())
            type_corrs = [evaluation_results['type_performance'][t]['correlation'] for t in type_names]
            
            colors = ['blue', 'orange', 'green', 'red', 'purple', 'brown'][:len(type_names)]
            bars = plt.bar(range(len(type_names)), type_corrs, color=colors, alpha=0.7)
            plt.title('不同数据类型的相关性')
            plt.ylabel('相关性')
            plt.xticks(range(len(type_names)), [t.replace('_', '\n') for t in type_names], rotation=45, ha='right')
            plt.axhline(y=0.5, color='green', linestyle='--', alpha=0.5, label='良好阈值')
            plt.legend()
            
            # 评分分布
            plt.subplot(2, 3, 5)
            plt.hist(test_weak_labels, bins=20, alpha=0.5, label='弱标签', color='blue')
            plt.hist(scores, bins=20, alpha=0.5, label='网络评分', color='orange')
            plt.title('评分分布对比')
            plt.xlabel('评分值')
            plt.ylabel('频次')
            plt.legend()
            
            # 样本展示
            plt.subplot(2, 3, 6)
            # 选择几个代表性样本
            n_samples = min(6, len(test_images))
            indices = np.linspace(0, len(test_images)-1, n_samples, dtype=int)
            
            sample_scores = scores[indices]
            sample_labels = test_weak_labels[indices]
            
            x_pos = np.arange(n_samples)
            width = 0.35
            
            plt.bar(x_pos - width/2, sample_labels, width, label='弱标签', alpha=0.7)
            plt.bar(x_pos + width/2, sample_scores, width, label='网络评分', alpha=0.7)
            plt.title('样本对比')
            plt.xlabel('样本索引')
            plt.ylabel('评分')
            plt.legend()
            
            plt.tight_layout()
            plt.savefig('step2_training_results.png', dpi=150, bbox_inches='tight')
            plt.show()
        
        # 2. 分割结果可视化
        plt.figure(figsize=(18, 12))
        
        # 选择一些代表性样本进行可视化
        n_vis_samples = min(8, len(test_images))
        vis_indices = np.linspace(0, len(test_images)-1, n_vis_samples, dtype=int)
        
        for i, idx in enumerate(vis_indices):
            # 原始图像
            plt.subplot(3, n_vis_samples, i+1)
            plt.imshow(test_images[idx])
            plt.title(f'Image {idx}\nReward: {test_rewards[idx]:.2f}')
            plt.axis('off')
            
            # 概率图
            plt.subplot(3, n_vis_samples, n_vis_samples + i+1)
            prob_map = prob_maps[idx, :, :, 0]
            plt.imshow(prob_map, cmap='RdYlGn', vmin=0, vmax=1)
            plt.title(f'Probability Map\nScore: {scores[idx]:.3f}')
            plt.axis('off')
            
            # 对比
            plt.subplot(3, n_vis_samples, 2*n_vis_samples + i+1)
            plt.bar(['Label', 'Pred'], [test_weak_labels[idx], scores[idx]], 
                   color=['blue', 'orange'], alpha=0.7)
            plt.title('Label vs Prediction')
            plt.ylim(0, 1)
        
        plt.tight_layout()
        plt.savefig('step2_segmentation_visualization.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✅ 可视化完成")
    
    def run_validation(self):
        """运行完整的第二步验证"""
        print("🎯 开始验证第二步：分割网络训练")
        print("=" * 60)
        
        # 1. 加载数据
        images, rewards, data_types = self.load_training_data(max_samples=150)
        if images is None:
            print("❌ 数据加载失败")
            return
        
        # 2. 划分训练和测试集
        n_train = int(0.8 * len(images))
        train_images, test_images = images[:n_train], images[n_train:]
        train_rewards, test_rewards = rewards[:n_train], rewards[n_train:]
        train_data_types, test_data_types = data_types[:n_train], data_types[n_train:]
        
        print(f"📊 数据划分:")
        print(f"   训练集: {len(train_images)} 样本")
        print(f"   测试集: {len(test_images)} 样本")
        
        # 3. 创建训练器
        self.create_trainer()
        
        # 4. 添加训练数据
        self.add_training_data(train_images, train_rewards, train_data_types)
        
        # 5. 训练网络
        best_loss = self.train_network(num_epochs=30)
        
        # 6. 评估网络
        evaluation_results, outputs = self.evaluate_network(test_images, test_rewards, test_data_types)
        
        if evaluation_results is None:
            print("❌ 评估失败")
            return
        
        # 7. 可视化结果
        self.visualize_results(test_images, test_rewards, test_data_types, outputs, evaluation_results)
        
        # 8. 生成报告
        self.generate_report(best_loss, evaluation_results)
    
    def generate_report(self, best_loss, evaluation_results):
        """生成验证报告"""
        print("\n📋 第二步验证报告")
        print("=" * 40)
        
        print(f"🚀 训练结果:")
        print(f"   最佳损失: {best_loss:.6f}")
        print(f"   训练轮数: {len(self.training_history)}")
        
        print(f"\n🎯 评估结果:")
        print(f"   整体相关性: {evaluation_results['overall_correlation']:.3f}")
        print(f"   评分范围: [{evaluation_results['score_range'][0]:.3f}, {evaluation_results['score_range'][1]:.3f}]")
        print(f"   评分均值: {evaluation_results['score_mean']:.3f} ± {evaluation_results['score_std']:.3f}")
        
        print(f"\n📊 不同数据类型性能:")
        for data_type, perf in evaluation_results['type_performance'].items():
            status = "✅" if perf['correlation'] > 0.5 else "⚠️" if perf['correlation'] > 0.3 else "❌"
            print(f"   {data_type}: 相关性={perf['correlation']:.3f}, 样本数={perf['count']} {status}")
        
        # 保存完整报告
        report = {
            'training_history': self.training_history,
            'best_loss': best_loss,
            'evaluation_results': evaluation_results,
            'timestamp': str(np.datetime64('now'))
        }
        
        with open('step2_validation_report.pkl', 'wb') as f:
            pickle.dump(report, f)
        
        print(f"\n💾 完整报告已保存到: step2_validation_report.pkl")
        print(f"📈 可视化结果已保存到: step2_training_results.png, step2_segmentation_visualization.png")


def main():
    """主函数"""
    validator = Step2Validator()
    validator.run_validation()


if __name__ == "__main__":
    main()
